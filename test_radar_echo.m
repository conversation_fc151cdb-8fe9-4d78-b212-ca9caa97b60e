% =========================================================================
%                   雷达回波功能测试脚本
% =========================================================================
%
% 测试新添加的雷达回波计算功能
%
% =========================================================================

%% 测试参数
clear; clc;

% 创建测试雷达
radar.position = [0, -20, 15];
radar.range = 200;
radar.fov_h = 60;
radar.fov_v = 30;
radar.tilt = -15;
radar.azimuth = 0;

% 雷达参数
radar_freq = 77e9;
c = 3e8;
bandwidth = 4e9;
threshold = -60;

% 测试目标位置
target_positions = [
    0, 50, 10;    % 目标1：正前方
    10, 60, 5;    % 目标2：右前方
    -10, 70, 8;   % 目标3：左前方
    0, 250, 20;   % 目标4：超出范围
    50, 30, 10;   % 目标5：超出视场角
];

target_ids = [1, 2, 3, 4, 5];

fprintf('测试雷达回波计算功能...\n');
fprintf('雷达位置: [%.1f, %.1f, %.1f]\n', radar.position);
fprintf('探测范围: %.1f m\n', radar.range);
fprintf('视场角: %.1f° x %.1f°\n', radar.fov_h, radar.fov_v);
fprintf('俯仰角: %.1f°\n', radar.tilt);
fprintf('方位角: %.1f°\n', radar.azimuth);
fprintf('检测门限: %.1f dBm\n', threshold);
fprintf('------------------------------------------\n');

% 测试每个目标
for i = 1:size(target_positions, 1)
    target_pos = target_positions(i, :);
    distance = norm(target_pos - radar.position);
    
    fprintf('目标 %d: 位置 [%.1f, %.1f, %.1f], 距离 %.1f m\n', ...
        i, target_pos(1), target_pos(2), target_pos(3), distance);
    
    % 检查距离
    if distance > radar.range
        fprintf('  -> 超出探测范围\n');
        continue;
    end
    
    % 检查视场角
    in_fov = is_target_in_fov(radar.position, target_pos, radar.tilt, radar.azimuth, ...
                             radar.fov_h, radar.fov_v);
    if ~in_fov
        fprintf('  -> 超出视场角\n');
        continue;
    end
    
    % 计算回波功率
    golf_ball_radius = 0.02135;
    rcs = pi * golf_ball_radius^2;
    lambda = c / radar_freq;
    path_loss_db = 20 * log10(4 * pi * distance / lambda);
    
    tx_power_dbm = 20;
    antenna_gain_db = 20;
    rx_power_dbm = tx_power_dbm + 2 * antenna_gain_db + 10*log10(rcs) - 2*path_loss_db;
    
    fprintf('  -> 在探测范围内，回波功率: %.1f dBm', rx_power_dbm);
    
    if rx_power_dbm > threshold
        fprintf(' (检测到)\n');
    else
        fprintf(' (未检测到)\n');
    end
end

fprintf('------------------------------------------\n');

% 使用完整的回波计算函数测试
fprintf('使用完整回波计算函数测试:\n');
[detected_targets, echo_powers] = calculate_radar_echo(radar, target_positions, target_ids, ...
    radar_freq, c, bandwidth, threshold);

if ~isempty(detected_targets)
    fprintf('检测到 %d 个目标:\n', length(detected_targets));
    for i = 1:length(detected_targets)
        fprintf('  目标 %d: 回波功率 %.1f dBm\n', detected_targets(i), echo_powers(i));
    end
else
    fprintf('未检测到任何目标\n');
end

fprintf('测试完成。\n');

%% 辅助函数定义

% 辅助函数：计算雷达回波
function [detected_targets, echo_powers] = calculate_radar_echo(radar, target_positions, target_ids, ...
    radar_freq, c, bandwidth, threshold)
    % radar: 雷达系统结构
    % target_positions: 目标位置矩阵 [N x 3]
    % target_ids: 目标ID数组
    % radar_freq: 雷达频率 (Hz)
    % c: 光速 (m/s)
    % bandwidth: 扫频带宽 (Hz)
    % threshold: 检测门限 (dBm)
    
    detected_targets = [];
    echo_powers = [];
    
    if isempty(target_positions)
        return;
    end
    
    radar_pos = radar.position;
    
    for i = 1:size(target_positions, 1)
        target_pos = target_positions(i, :);
        
        % 计算距离
        distance = norm(target_pos - radar_pos);
        
        % 检查是否在雷达探测范围内
        if distance > radar.range
            continue;
        end
        
        % 检查是否在雷达视场角内
        if ~is_target_in_fov(radar_pos, target_pos, radar.tilt, radar.azimuth, ...
                             radar.fov_h, radar.fov_v)
            continue;
        end
        
        % 计算雷达截面积 (RCS) - 高尔夫球的典型RCS
        golf_ball_radius = 0.02135; % 高尔夫球半径 (m)
        rcs = pi * golf_ball_radius^2; % 简化的RCS计算
        
        % 计算路径损耗 (自由空间路径损耗)
        lambda = c / radar_freq;
        path_loss_db = 20 * log10(4 * pi * distance / lambda);
        
        % 计算雷达方程中的回波功率
        % P_r = P_t * G_t * G_r * lambda^2 * RCS / ((4*pi)^3 * R^4)
        % 简化计算，假设发射功率为20dBm，天线增益为20dB
        tx_power_dbm = 20;          % 发射功率 (dBm)
        antenna_gain_db = 20;       % 天线增益 (dB)
        
        % 计算接收功率
        rx_power_dbm = tx_power_dbm + 2 * antenna_gain_db + 10*log10(rcs) - 2*path_loss_db;
        
        % 添加一些随机噪声以模拟真实环境
        noise_std = 2; % 噪声标准差 (dB)
        rx_power_dbm = rx_power_dbm + noise_std * randn();
        
        % 检查是否超过检测门限
        if rx_power_dbm > threshold
            detected_targets = [detected_targets, target_ids(i)];
            echo_powers = [echo_powers, rx_power_dbm];
        end
    end
end

% 辅助函数：检查目标是否在雷达视场角内
function in_fov = is_target_in_fov(radar_pos, target_pos, tilt_deg, azimuth_deg, fov_h_deg, fov_v_deg)
    % 计算目标相对于雷达的方向向量
    direction = target_pos - radar_pos;
    direction = direction / norm(direction); % 归一化
    
    % 转换角度为弧度
    tilt_rad = deg2rad(tilt_deg);
    azimuth_rad = deg2rad(azimuth_deg);
    fov_h_rad = deg2rad(fov_h_deg);
    fov_v_rad = deg2rad(fov_v_deg);
    
    % 计算雷达指向的单位向量（考虑俯仰角和方位角）
    radar_direction = [sin(azimuth_rad) * cos(tilt_rad);
                      cos(azimuth_rad) * cos(tilt_rad);
                      sin(tilt_rad)];
    
    % 计算目标方向与雷达指向的夹角
    cos_angle = dot(direction, radar_direction);
    angle_rad = acos(max(-1, min(1, cos_angle))); % 限制在有效范围内
    
    % 简化的视场角检查（使用圆锥形近似）
    max_fov_rad = max(fov_h_rad, fov_v_rad) / 2;
    in_fov = angle_rad <= max_fov_rad;
end
