% =========================================================================
%           高尔夫球场雷达监控系统 - FMCW雷达RD图可视化 (修复版)
% =========================================================================
%
% 修复了维度匹配问题的版本
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

fprintf('启动修复版RD图可视化系统...\n');

% --- 高尔夫球场布局参数 ---
n = 3;                      % 减少球数量以提高性能
tee_spacing = 10;
course_length = 250;
course_width = 60;
tee_area_size = 8;

% --- 雷达系统参数 ---
main_radar_count = 3;
main_radar_height = 15;
main_radar_distance = -20;
main_radar_spacing = 25;
sub_radar_distance = 50;
sub_radar_height = 8;
sub_radar_count = 1;

% --- FMCW雷达参数 ---
radar_frequency = 77e9;
propagation_speed = physconst('LightSpeed');
chirp_time = 56e-6;
idle_time = 14e-6;
sweep_slope = 1e12;
sweep_bandwidth = chirp_time * sweep_slope;
num_chirps_per_frame = 64;
frame_time = 40e-3;
adc_sampling_freq = 5e6;
num_adc_samples = 268;
num_tx_antennas = 2;
num_rx_antennas = 4;
num_virtual_antennas = num_tx_antennas * num_rx_antennas;

% --- 高尔夫球物理参数 ---
min_v0 = 50; max_v0 = 80;
min_angle = 10; max_angle = 12;
min_azimuth = -2.0; max_azimuth = 2.0;
max_launch_delay = 1.0;
dt = 0.02; % 增大时间步长提高性能

% 物理常量
g = 9.81; m = 0.0459; r = 0.02135; A = pi * r^2; rho = 1.225; Cd = 0.4;

% --- RD图处理参数 ---
range_fft_size = 256;   % 减小FFT大小提高性能
vel_fft_size = 64;
rd_update_interval = 10; % 增大更新间隔

%% 2. 创建布局
fprintf('创建球场布局...\n');

% 计算位置
tee_positions = zeros(n, 3);
for i = 1:n
    tee_positions(i, :) = [(i-1) * tee_spacing - (n-1)*tee_spacing/2, 0, 0];
end

main_radar_positions = zeros(main_radar_count, 3);
for i = 1:main_radar_count
    x_offset = (i-2) * main_radar_spacing;
    main_radar_positions(i, :) = [x_offset, main_radar_distance, main_radar_height];
end

sub_radar_positions = [0, sub_radar_distance, sub_radar_height];

total_radars = main_radar_count + sub_radar_count;
all_radar_positions = [main_radar_positions; sub_radar_positions];
radar_names = {'左侧主雷达', '中央主雷达', '右侧主雷达', '子雷达1'};

%% 3. 初始化高尔夫球
fprintf('初始化高尔夫球...\n');

balls(n) = struct('id', [], 'pos', [], 'vel', [], 'path', [], ...
                  'launch_time', [], 'is_active', false, 'has_launched', false);

for i = 1:n
    balls(i).id = i;
    elevation_deg = min_angle + (max_angle - min_angle) * rand();
    elevation_rad = deg2rad(elevation_deg);
    azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();
    azimuth_rad = deg2rad(azimuth_deg);
    balls(i).launch_time = max_launch_delay * rand();
    
    v0_rand = min_v0 + (max_v0 - min_v0) * rand();
    balls(i).pos = [tee_positions(i, 1); tee_positions(i, 2); 1.0];
    
    v_xy = v0_rand * cos(elevation_rad);
    v_z = v0_rand * sin(elevation_rad);
    v_x = v_xy * sin(azimuth_rad);
    v_y = v_xy * cos(azimuth_rad);
    balls(i).vel = [v_x; v_y; v_z];
    
    balls(i).path = [];
    balls(i).is_active = false;
    balls(i).has_launched = false;
    
    fprintf('球 %d: 时间=%.2fs, 速度=%.2fm/s\n', i, balls(i).launch_time, v0_rand);
end

%% 4. 创建可视化窗口
fprintf('创建可视化窗口...\n');

% 主窗口
main_fig = figure('Name', '3D场景', 'Position', [50, 100, 700, 500], 'Color', 'w');
hold on; grid on; box on;
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
view(45, 20); axis equal;

x_range = [-course_width/2, course_width/2];
y_range = [main_radar_distance-10, course_length];
z_range = [0, main_radar_height+5];
xlim(x_range); ylim(y_range); zlim(z_range);

% 绘制球场
[X_ground, Y_ground] = meshgrid(x_range(1):10:x_range(2), y_range(1):10:y_range(2));
Z_ground = zeros(size(X_ground));
surf(X_ground, Y_ground, Z_ground, 'FaceColor', [0.2, 0.8, 0.2], 'FaceAlpha', 0.3, 'EdgeColor', 'none');

% 绘制击球区域
for i = 1:n
    pos = tee_positions(i, :);
    x_tee = pos(1) + [-tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2, -tee_area_size/2];
    y_tee = pos(2) + [-tee_area_size/2, -tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2];
    z_tee = [0.1, 0.1, 0.1, 0.1, 0.1];
    plot3(x_tee, y_tee, z_tee, 'k-', 'LineWidth', 2);
    fill3(x_tee(1:4), y_tee(1:4), z_tee(1:4), [0.8, 0.6, 0.4], 'FaceAlpha', 0.7);
    text(pos(1), pos(2), pos(3)+2, sprintf('区域%d', i), 'HorizontalAlignment', 'center');
end

% 绘制雷达
radar_colors = [1, 0, 0; 0, 0, 1; 0, 1, 0; 1, 0.5, 0];
for i = 1:total_radars
    pos = all_radar_positions(i, :);
    scatter3(pos(1), pos(2), pos(3), 100, radar_colors(i, :), 'filled', 's');
    text(pos(1), pos(2), pos(3)+2, radar_names{i}, 'HorizontalAlignment', 'center', 'Color', radar_colors(i, :));
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], 'Color', [0.5, 0.5, 0.5], 'LineWidth', 2);
end

% 初始化球轨迹
path_plots = gobjects(1, n);
ball_markers = gobjects(1, n);
colors = lines(n);
for i = 1:n
    path_plots(i) = plot3(NaN, NaN, NaN, '-', 'Color', colors(i,:), 'LineWidth', 2);
    ball_markers(i) = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', colors(i,:), 'MarkerEdgeColor', 'k', 'MarkerSize', 8);
end

title('高尔夫球场雷达监控系统 - 3D场景');

% RD图窗口
rd_fig = figure('Name', 'Range-Doppler图', 'Position', [800, 100, 800, 600], 'Color', 'w');

% 计算坐标轴
range_res = propagation_speed / (2 * sweep_bandwidth);
max_range = range_res * range_fft_size / 2;
range_bins = linspace(0, max_range, range_fft_size/2);

vel_res = propagation_speed / (2 * radar_frequency * (chirp_time + idle_time) * num_chirps_per_frame);
max_vel = vel_res * vel_fft_size / 2;
vel_bins = linspace(-max_vel, max_vel, vel_fft_size);

for i = 1:total_radars
    subplot(2, 2, i);
    imagesc(range_bins, vel_bins, zeros(vel_fft_size, range_fft_size/2));
    axis xy; colorbar; colormap('jet');
    title(radar_names{i}); xlabel('距离 (m)'); ylabel('速度 (m/s)');
    caxis([-80, -20]); grid on;
end

%% 5. 动态仿真循环
fprintf('开始动态仿真...\n');

sim_time = 0;
num_finished_balls = 0;
frame_counter = 0;

while num_finished_balls < n
    % 更新球状态
    for i = 1:n
        if sim_time >= balls(i).launch_time && ~balls(i).has_launched
            balls(i).has_launched = true;
            balls(i).is_active = true;
            balls(i).path = balls(i).pos;
            fprintf('球 %d 发射！\n', i);
        end
        
        if balls(i).is_active
            v_mag = norm(balls(i).vel);
            F_drag = -0.5 * rho * A * Cd * v_mag * balls(i).vel;
            F_net = [0; 0; -m*g] + F_drag;
            a = F_net / m;
            
            balls(i).vel = balls(i).vel + a * dt;
            balls(i).pos = balls(i).pos + balls(i).vel * dt;
            balls(i).path = [balls(i).path, balls(i).pos];
            
            if balls(i).pos(3) < 0
                balls(i).is_active = false;
                num_finished_balls = num_finished_balls + 1;
                fprintf('球 %d 落地！\n', i);
            end
        end
    end
    
    % 收集活跃球位置
    active_ball_positions = [];
    for i = 1:n
        if balls(i).is_active
            active_ball_positions = [active_ball_positions; balls(i).pos'];
        end
    end
    
    % RD图处理
    if ~isempty(active_ball_positions) && mod(frame_counter, rd_update_interval) == 0
        for radar_idx = 1:total_radars
            radar_pos = all_radar_positions(radar_idx, :);
            
            % 生成简化的RD图
            rd_map = generate_simple_rd_map(radar_pos, active_ball_positions, ...
                range_bins, vel_bins, range_fft_size, vel_fft_size);
            
            % 更新显示
            figure(rd_fig);
            subplot(2, 2, radar_idx);
            imagesc(range_bins, vel_bins, rd_map);
            axis xy; colorbar; colormap('jet');
            title(sprintf('%s - 帧 %d', radar_names{radar_idx}, frame_counter));
            xlabel('距离 (m)'); ylabel('速度 (m/s)');
            caxis([-80, -20]); grid on;
        end
    end
    frame_counter = frame_counter + 1;
    
    % 更新3D显示
    figure(main_fig);
    for i = 1:n
        if balls(i).has_launched
            set(path_plots(i), 'XData', balls(i).path(1,:), 'YData', balls(i).path(2,:), 'ZData', balls(i).path(3,:));
            if balls(i).is_active
                set(ball_markers(i), 'XData', balls(i).pos(1), 'YData', balls(i).pos(2), 'ZData', balls(i).pos(3));
            else
                final_pos = balls(i).path(:, end);
                final_pos(3) = 0;
                set(ball_markers(i), 'XData', final_pos(1), 'YData', final_pos(2), 'ZData', final_pos(3));
            end
        end
    end
    
    drawnow limitrate;
    sim_time = sim_time + dt;
    
    if sim_time > 20
        fprintf('仿真超时结束。\n');
        break;
    end
end

fprintf('仿真完成！\n');

%% 辅助函数

function rd_map = generate_simple_rd_map(radar_pos, target_positions, range_bins, vel_bins, range_fft_size, vel_fft_size)
    % 简化的RD图生成，避免复杂的FFT处理
    rd_map = -80 * ones(vel_fft_size, range_fft_size/2); % 噪声底
    
    for i = 1:size(target_positions, 1)
        target_pos = target_positions(i, :);
        distance = norm(target_pos - radar_pos);
        
        % 找到最近的距离bin
        [~, range_idx] = min(abs(range_bins - distance));
        
        % 假设目标速度为0（简化）
        vel_idx = round(vel_fft_size / 2);
        
        % 在RD图中添加目标
        if range_idx > 0 && range_idx <= length(range_bins) && vel_idx > 0 && vel_idx <= vel_fft_size
            % 添加目标峰值
            rd_map(vel_idx, range_idx) = -30; % 强回波
            
            % 添加一些扩散
            for dr = -2:2
                for dv = -1:1
                    r_idx = range_idx + dr;
                    v_idx = vel_idx + dv;
                    if r_idx > 0 && r_idx <= size(rd_map, 2) && v_idx > 0 && v_idx <= size(rd_map, 1)
                        rd_map(v_idx, r_idx) = max(rd_map(v_idx, r_idx), -40 - abs(dr)*5 - abs(dv)*10);
                    end
                end
            end
        end
    end
end
