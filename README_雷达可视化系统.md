# 高尔夫球场雷达监控系统3D可视化

## 概述

本项目实现了一个高尔夫球场雷达监控系统的3D可视化，展示了完整的雷达布局、覆盖区域和监控策略。系统包含主雷达系统（后端）和子雷达系统（中段监控），能够为高尔夫球场提供全方位的监控覆盖。

## 文件说明

### 主要文件

1. **golf_radar_simple.m** - 简化版可视化
   - 基础的3D布局展示
   - 雷达设备的几何表示
   - 简单的覆盖区域指示线
   - 适合快速预览和基本演示

2. **golf_radar_enhanced.m** - 增强版可视化
   - 完整的3D雷达覆盖锥体
   - 详细的覆盖区域可视化
   - 点云表示的监控范围
   - 更丰富的视觉效果

3. **golf_radar_visualization.m** - 完整版可视化
   - 最详细的覆盖区域计算
   - 高精度的3D锥体绘制
   - 完整的雷达参数配置

## 系统特性

### 高尔夫球场布局
- **击球区域数量**: 5个（可扩展至10个）
- **区域间距**: 10米
- **球场总长度**: 250米
- **球场总宽度**: 60米
- **击球区域大小**: 8米×8米

### 主雷达系统（后端）
- **数量**: 3个（左、中、右）
- **安装高度**: 15米
- **安装位置**: 球场后方20米
- **雷达间距**: 25米
- **俯视角度**: -15度
- **内倾角度**: ±10度（左右雷达）

### 子雷达系统（中段监控）
- **安装位置**: 距离起点50米
- **安装高度**: 8米
- **覆盖策略**: 每5个击球区域配置1个子雷达
- **监控角度**: -10度俯视

### 雷达技术参数
- **主雷达探测距离**: 200米
- **主雷达水平视场角**: 60度
- **主雷达垂直视场角**: 30度
- **子雷达探测距离**: 80米
- **子雷达水平视场角**: 45度
- **子雷达垂直视场角**: 25度

## 使用方法

### 运行简化版
```matlab
run('golf_radar_simple.m');
```

### 运行增强版
```matlab
run('golf_radar_enhanced.m');
```

### 运行完整版
```matlab
run('golf_radar_visualization.m');
```

## 可视化功能

### 3D组件
1. **高尔夫球场地面** - 绿色半透明表面
2. **击球区域** - 棕色方形平台，带编号标签
3. **主雷达设备** - 彩色长方体（红、蓝、绿）
4. **子雷达设备** - 橙色长方体
5. **支撑杆** - 灰色支撑结构
6. **覆盖区域** - 透明锥体或点云表示

### 交互功能
- **视角旋转**: 鼠标拖拽
- **缩放**: 鼠标滚轮
- **图例**: 右上角显示各组件说明
- **坐标轴**: 带单位的3D坐标系

## 参数配置

### 可调整参数
```matlab
% 球场参数
n = 5;                      % 击球区域数量
tee_spacing = 10;           % 区域间距
course_length = 250;        % 球场长度
course_width = 60;          % 球场宽度

% 主雷达参数
main_radar_height = 15;     % 安装高度
main_radar_distance = -20;  % 距离起点
main_radar_spacing = 25;    % 雷达间距
main_radar_range = 200;     % 探测距离

% 子雷达参数
sub_radar_distance = 50;    % 距离起点
sub_radar_height = 8;       % 安装高度
sub_radar_range = 80;       % 探测距离

% 可视化参数
coverage_alpha = 0.15;      % 覆盖区域透明度
```

## 技术实现

### 核心算法
1. **坐标变换** - 球坐标到直角坐标系转换
2. **旋转矩阵** - 俯仰角和方位角的3D旋转
3. **锥体生成** - 基于视场角的覆盖区域计算
4. **碰撞检测** - 地面约束的覆盖范围限制

### 关键函数
- `draw_radar_box()` - 绘制雷达设备3D模型
- `draw_radar_coverage_cone()` - 绘制覆盖锥体
- 坐标变换和旋转计算

## 应用场景

### 监控策略分析
- 评估雷达覆盖的完整性
- 识别监控盲区
- 优化雷达布局方案

### 系统设计验证
- 验证雷达安装位置的合理性
- 分析不同配置的覆盖效果
- 支持系统参数调优

### 演示和培训
- 直观展示监控系统架构
- 培训操作人员理解系统布局
- 向客户展示技术方案

## 扩展功能

### 可扩展特性
1. **击球区域数量** - 支持最多10个区域
2. **雷达类型** - 可添加不同规格的雷达
3. **覆盖算法** - 支持更复杂的覆盖计算
4. **动态仿真** - 可添加目标轨迹仿真

### 未来改进
- 添加实时数据接口
- 集成雷达信号处理
- 支持多场景配置
- 增加性能分析工具

## 系统要求

- MATLAB R2018b或更高版本
- 3D图形支持
- 足够的内存用于复杂场景渲染

## 注意事项

1. 函数定义必须放在脚本文件末尾
2. 复杂覆盖区域计算可能影响性能
3. 建议先运行简化版测试环境
4. 可根据需要调整透明度和颜色参数

## 联系信息

如有问题或建议，请联系开发团队。
