%% 验证MIMORADAR类的新属性
clear; clc;

fprintf('=== 验证MIMORADAR类的高级帧模式属性 ===\n\n');

try
    % 创建MIMORADAR对象
    fmcw = MIMORADAR;
    fprintf('✓ MIMORADAR对象创建成功\n');
    
    % 测试高级帧模式属性
    fprintf('\n测试高级帧模式属性:\n');
    
    % 基本开关属性
    fprintf('  advancedFrameMode: %s\n', mat2str(fmcw.advancedFrameMode));
    
    % 第二子帧FMCW参数
    fprintf('  radarFrequency2: %.2e Hz\n', fmcw.radarFrequency2);
    fprintf('  chirpTRandEndime2: %.2e s\n', fmcw.chirpTRandEndime2);
    fprintf('  Tidle2: %.2e s\n', fmcw.Tidle2);
    fprintf('  sweepBw2: %.2e Hz/s\n', fmcw.sweepBw2);
    fprintf('  Bw2: %.2e Hz\n', fmcw.Bw2);
    fprintf('  NumOfChirpInOneSubframe2: %d\n', fmcw.NumOfChirpInOneSubframe2);
    
    % 时间管理属性
    fprintf('  activateTimeSubframe1: %.2e s\n', fmcw.activateTimeSubframe1);
    fprintf('  activateTimeSubframe2: %.2e s\n', fmcw.activateTimeSubframe2);
    fprintf('  subframeGap: %.2e s\n', fmcw.subframeGap);
    
    % 测试属性设置
    fprintf('\n测试属性设置:\n');
    fmcw.advancedFrameMode = true;
    fprintf('  设置 advancedFrameMode = true: %s\n', mat2str(fmcw.advancedFrameMode));
    
    fmcw.radarFrequency2 = 78e9;
    fprintf('  设置 radarFrequency2 = 78e9: %.2e Hz\n', fmcw.radarFrequency2);
    
    fprintf('\n✓ 所有高级帧模式属性验证成功！\n');
    
catch ME
    fprintf('❌ 错误: %s\n', ME.message);
    fprintf('   位置: %s (第%d行)\n', ME.stack(1).file, ME.stack(1).line);
end

fprintf('\n=== 验证完成 ===\n');
