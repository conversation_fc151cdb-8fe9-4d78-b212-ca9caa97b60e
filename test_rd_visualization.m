% =========================================================================
%                   RD图可视化功能测试脚本
% =========================================================================
%
% 测试FMCW雷达立方体生成和Range-Doppler图处理功能
%
% =========================================================================

%% 1. 基本参数设置
clear; clc; close all;

fprintf('开始测试RD图可视化功能...\n');

% FMCW雷达参数
radar_frequency = 77e9;
propagation_speed = physconst('LightSpeed');
chirp_time = 56e-6;
idle_time = 14e-6;
sweep_slope = 1e12;
sweep_bandwidth = chirp_time * sweep_slope;
num_chirps_per_frame = 64;
adc_sampling_freq = 5e6;
num_adc_samples = 268;
num_tx_antennas = 2;
num_rx_antennas = 4;
num_virtual_antennas = num_tx_antennas * num_rx_antennas;

% RD图处理参数
range_fft_size = 512;
vel_fft_size = 128;

fprintf('雷达参数设置完成。\n');

%% 2. 创建测试目标
fprintf('创建测试目标...\n');

% 雷达位置
radar_pos = [0, -20, 15];

% 测试目标位置（模拟高尔夫球轨迹上的几个点）
test_targets = [
    0, 10, 5;    % 目标1：近距离
    5, 30, 8;    % 目标2：中距离
    -3, 50, 12;  % 目标3：远距离
];

fprintf('测试目标数量: %d\n', size(test_targets, 1));

%% 3. 创建FMCW处理器
fprintf('创建FMCW处理器...\n');

processor = struct();
processor.position = radar_pos;
processor.frequency = radar_frequency;
processor.bandwidth = sweep_bandwidth;
processor.chirp_time = chirp_time;
processor.idle_time = idle_time;
processor.num_chirps = num_chirps_per_frame;
processor.num_samples = num_adc_samples;
processor.sampling_freq = adc_sampling_freq;
processor.num_tx = num_tx_antennas;
processor.num_rx = num_rx_antennas;
processor.lambda = propagation_speed / radar_frequency;
processor.sweep_slope = sweep_bandwidth / chirp_time;

fprintf('FMCW处理器创建完成。\n');

%% 4. 生成雷达数据立方体
fprintf('生成雷达数据立方体...\n');

radar_cube = generate_test_radar_cube(radar_pos, test_targets, processor, ...
    num_chirps_per_frame, num_virtual_antennas, num_adc_samples);

fprintf('雷达数据立方体维度: [%d, %d, %d] (chirps, rx, samples)\n', size(radar_cube));

%% 5. 生成Range-Doppler图
fprintf('生成Range-Doppler图...\n');

[rd_map, ~, ~] = generate_test_rd_map(radar_cube, range_fft_size, vel_fft_size);

fprintf('RD图维度: [%d, %d] (velocity bins, range bins)\n', size(rd_map));

%% 6. 计算坐标轴
fprintf('计算坐标轴...\n');

% 距离轴
range_res = propagation_speed / (2 * sweep_bandwidth);
max_range = range_res * range_fft_size / 2;
range_bins = linspace(0, max_range, range_fft_size/2);

% 速度轴
vel_res = propagation_speed / (2 * radar_frequency * (chirp_time + idle_time) * num_chirps_per_frame);
max_vel = vel_res * vel_fft_size / 2;
vel_bins = linspace(-max_vel, max_vel, vel_fft_size);

fprintf('距离分辨率: %.2f m, 最大距离: %.1f m\n', range_res, max_range);
fprintf('速度分辨率: %.2f m/s, 最大速度: %.1f m/s\n', vel_res, max_vel);

%% 7. 可视化结果
fprintf('创建可视化...\n');

figure('Name', 'RD图测试结果', 'Position', [100, 100, 1200, 800], 'Color', 'w');

% 子图1：3D场景
subplot(2, 2, 1);
hold on;
grid on;

% 绘制雷达位置
scatter3(radar_pos(1), radar_pos(2), radar_pos(3), 100, 'r', 'filled', 's');
text(radar_pos(1), radar_pos(2), radar_pos(3)+2, '雷达', 'HorizontalAlignment', 'center');

% 绘制目标位置
for i = 1:size(test_targets, 1)
    scatter3(test_targets(i,1), test_targets(i,2), test_targets(i,3), 50, 'b', 'filled', 'o');
    text(test_targets(i,1), test_targets(i,2), test_targets(i,3)+1, ...
         sprintf('目标%d', i), 'HorizontalAlignment', 'center');
end

xlabel('X (m)');
ylabel('Y (m)');
zlabel('Z (m)');
title('测试场景布局');
view(45, 20);
axis equal;

% 子图2：RD图
subplot(2, 2, [2, 4]);
imagesc(range_bins, vel_bins, rd_map);
axis xy;
colorbar;
colormap('jet');
title('Range-Doppler Map', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('距离 (m)', 'FontSize', 12);
ylabel('速度 (m/s)', 'FontSize', 12);
grid on;
caxis([-80, -20]);

% 子图3：距离剖面
subplot(2, 2, 3);
range_profile = max(rd_map, [], 1);
plot(range_bins, range_profile, 'b-', 'LineWidth', 2);
grid on;
xlabel('距离 (m)');
ylabel('功率 (dB)');
title('距离剖面');

fprintf('可视化完成。\n');

%% 8. 验证结果
fprintf('\n=== 测试结果验证 ===\n');

% 计算预期的距离bin
for i = 1:size(test_targets, 1)
    distance = norm(test_targets(i, :) - radar_pos);
    expected_range_bin = round(distance / range_res) + 1;
    fprintf('目标 %d: 实际距离 %.1f m, 预期距离bin %d\n', i, distance, expected_range_bin);
end

% 找到RD图中的峰值
[max_val, max_idx] = max(rd_map(:));
[vel_idx, range_idx] = ind2sub(size(rd_map), max_idx);
detected_range = range_bins(range_idx);
detected_vel = vel_bins(vel_idx);

fprintf('检测到的最强目标: 距离 %.1f m, 速度 %.2f m/s, 功率 %.1f dB\n', ...
    detected_range, detected_vel, max_val);

fprintf('测试完成！\n');

%% 辅助函数定义

% 生成测试雷达数据立方体
function radar_cube = generate_test_radar_cube(radar_pos, target_positions, processor, ...
    num_chirps, num_virtual_rx, num_samples)

    radar_cube = zeros(num_chirps, num_virtual_rx, num_samples);

    if isempty(target_positions)
        return;
    end

    for chirp_idx = 1:num_chirps
        for target_idx = 1:size(target_positions, 1)
            target_pos = target_positions(target_idx, :);

            % 计算距离
            distance = norm(target_pos - radar_pos);

            % 计算距离频率
            range_freq = -processor.sweep_slope * 2 / physconst('LightSpeed') * distance;

            % 计算相位
            phase = processor.frequency * 2 / physconst('LightSpeed') * distance;

            % 生成距离信号
            sample_times = (0:num_samples-1) / processor.sampling_freq;
            range_signal = exp(1j * 2 * pi * range_freq * sample_times);

            % 添加相位
            range_signal = range_signal * exp(1j * 2 * pi * phase);

            % 简化的多普勒处理
            doppler_phase = 0;

            % 将信号添加到所有虚拟接收天线
            for rx_idx = 1:num_virtual_rx
                radar_cube(chirp_idx, rx_idx, :) = squeeze(radar_cube(chirp_idx, rx_idx, :))' + ...
                    range_signal * exp(1j * doppler_phase * chirp_idx) * 0.1; % 降低信号强度
            end
        end
    end

    % 添加噪声
    noise_power = 0.001;
    noise_real = sqrt(noise_power/2) * randn(size(radar_cube));
    noise_imag = sqrt(noise_power/2) * randn(size(radar_cube));
    noise = complex(noise_real, noise_imag);
    radar_cube = radar_cube + noise;
end

% 生成测试Range-Doppler图
function [rd_map, range_bins, vel_bins] = generate_test_rd_map(frame_data, range_fft_size, vel_fft_size)
    [num_chirps, num_rx, num_samples] = size(frame_data);

    % 距离FFT - 修复窗函数维度问题
    range_window = hamming(num_samples);
    range_window_3d = repmat(reshape(range_window, 1, 1, num_samples), [num_chirps, num_rx, 1]);
    windowed_data = frame_data .* range_window_3d;

    range_fft = fft(windowed_data, range_fft_size, 3);
    range_fft = range_fft(:, :, 1:range_fft_size/2);

    % 速度FFT - 修复窗函数维度问题
    vel_window = hamming(num_chirps);
    vel_window_3d = repmat(reshape(vel_window, num_chirps, 1, 1), [1, num_rx, range_fft_size/2]);
    windowed_range_data = range_fft .* vel_window_3d;

    vel_fft = fft(windowed_range_data, vel_fft_size, 1);
    vel_fft = fftshift(vel_fft, 1);

    % 多天线合并
    rd_map = squeeze(sum(abs(vel_fft).^2, 2));

    % 确保rd_map是2D矩阵
    if size(rd_map, 1) == 1 || size(rd_map, 2) == 1
        rd_map = reshape(rd_map, vel_fft_size, range_fft_size/2);
    end

    % 转换为dB
    rd_map = 10 * log10(rd_map + eps);

    range_bins = 1:range_fft_size/2;
    vel_bins = 1:vel_fft_size;
end
