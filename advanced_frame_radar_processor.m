%% 高级帧模式雷达数据处理器
% 功能：解析双子帧雷达数据，生成RD图，实现速度解模糊
% 作者：雷达信号处理系统
% 日期：2025年

function advanced_frame_radar_processor()
    %% 清理环境
    clear; clc; close all;

    % 设置中文字体支持
    try
        if ispc
            set(0, 'DefaultAxesFontName', 'SimHei');
            set(0, 'DefaultTextFontName', 'SimHei');
            set(0, 'DefaultUicontrolFontName', 'SimHei');
        elseif ismac
            set(0, 'DefaultAxesFontName', 'PingFang SC');
            set(0, 'DefaultTextFontName', 'PingFang SC');
            set(0, 'DefaultUicontrolFontName', 'PingFang SC');
        else
            set(0, 'DefaultAxesFontName', 'WenQuanYi Micro Hei');
            set(0, 'DefaultTextFontName', 'WenQuanYi Micro Hei');
            set(0, 'DefaultUicontrolFontName', 'WenQuanYi Micro Hei');
        end
    catch
        fprintf('警告: 无法设置中文字体，可能显示为方框\n');
    end

    fprintf('=== 高级帧模式雷达数据处理器 ===\n\n');

    %% 1. 数据加载和解析
    fprintf('1. 正在加载雷达数据...\n');
    try
        % 检查数据文件是否存在
        data_file = 'data_reduced_precision_advanced_frame.mat';
        if ~exist(data_file, 'file')
            error('数据文件 %s 不存在！请先运行高级帧模式生成数据。', data_file);
        end

        % 加载数据
        data = load(data_file);
        RawDataMatrix = data.RawDataMatrix;
        fmcw = data.fmcw;

        % 验证是否为高级帧模式数据
        if ~fmcw.advancedFrameMode
            error('加载的数据不是高级帧模式数据！');
        end

        fprintf('   ✓ 数据加载成功\n');
        fprintf('   数据维度: [%d, %d, %d, %d]\n', size(RawDataMatrix));
        fprintf('   帧数: %d\n', size(RawDataMatrix, 1));
        fprintf('   每帧总chirps: %d\n', size(RawDataMatrix, 2));
        fprintf('   虚拟天线数: %d\n', size(RawDataMatrix, 4));

    catch ME
        fprintf('❌ 数据加载失败: %s\n', ME.message);
        return;
    end

    %% 2. 解析双子帧数据结构
    fprintf('2. 解析双子帧数据结构...\n');

    % 提取子帧参数
    subframe1_chirps = fmcw.NumOfChirpInOneFrame;
    subframe2_chirps = fmcw.NumOfChirpInOneSubframe2;
    total_chirps = subframe1_chirps + subframe2_chirps;

    % 验证数据维度
    if size(RawDataMatrix, 2) ~= total_chirps
        error('数据维度不匹配！期望 %d chirps，实际 %d chirps', total_chirps, size(RawDataMatrix, 2));
    end

    % 分离子帧数据
    % 数据格式: [frame, chirp, sample, Rx]
    subframe1_data = RawDataMatrix(:, 1:subframe1_chirps, :, :);
    subframe2_data = RawDataMatrix(:, (subframe1_chirps+1):end, :, :);

    fprintf('   ✓ 数据结构解析完成\n');
    fprintf('   第一子帧: %d chirps (频率: %.2f GHz, 带宽: %.1f MHz)\n', ...
        subframe1_chirps, fmcw.radarFrequency/1e9, fmcw.Bw/1e6);
    fprintf('   第二子帧: %d chirps (频率: %.2f GHz, 带宽: %.1f MHz)\n', ...
        subframe2_chirps, fmcw.radarFrequency2/1e9, fmcw.Bw2/1e6);

    %% 3. 初始化处理参数
    fprintf('3. 初始化处理参数...\n');

    % 基本参数
    c = fmcw.propagationSpeed;
    num_frames = size(RawDataMatrix, 1);
    num_samples = size(RawDataMatrix, 3);
    num_rx = size(RawDataMatrix, 4);

    % 第一子帧参数
    params1 = struct();
    params1.fc = fmcw.radarFrequency;
    params1.B = fmcw.Bw;
    params1.T = fmcw.chirpTRandEndime;
    params1.Tidle = fmcw.Tidle;
    params1.fs = fmcw.adcSamplingFrequency;
    params1.num_chirps = subframe1_chirps;

    % 第二子帧参数
    params2 = struct();
    params2.fc = fmcw.radarFrequency2;
    params2.B = fmcw.Bw2;
    params2.T = fmcw.chirpTRandEndime2;
    params2.Tidle = fmcw.Tidle2;
    params2.fs = fmcw.adcSamplingFrequency;
    params2.num_chirps = subframe2_chirps;

    % 计算分辨率参数
    [range_res1, vel_res1, max_range1, max_vel1] = calculate_resolution(params1, c);
    [range_res2, vel_res2, max_range2, max_vel2] = calculate_resolution(params2, c);

    fprintf('   第一子帧 - 距离分辨率: %.2f m, 速度分辨率: %.2f m/s\n', range_res1, vel_res1);
    fprintf('   第二子帧 - 距离分辨率: %.2f m, 速度分辨率: %.2f m/s\n', range_res2, vel_res2);
    fprintf('   ✓ 参数初始化完成\n');

    %% 4. 创建动态可视化界面
    fprintf('4. 创建可视化界面...\n');

    % 创建主窗口 - 增加高度以容纳控制面板
    fig = figure('Name', 'Advanced Frame Mode Radar Processor', 'Position', [100, 100, 1400, 900]);

    % 创建控制面板 - 放在顶部，不遮挡子图
    control_panel = uipanel('Parent', fig, 'Title', 'Playback Controls', ...
        'Position', [0.02, 0.88, 0.96, 0.10], 'FontSize', 10);

    % 播放控制按钮 - 使用英文避免字体问题
    play_btn = uicontrol('Parent', control_panel, 'Style', 'pushbutton', ...
        'String', 'Play', 'Position', [20, 15, 60, 25], 'FontSize', 9);
    pause_btn = uicontrol('Parent', control_panel, 'Style', 'pushbutton', ...
        'String', 'Pause', 'Position', [90, 15, 60, 25], 'FontSize', 9);
    step_btn = uicontrol('Parent', control_panel, 'Style', 'pushbutton', ...
        'String', 'Step', 'Position', [160, 15, 60, 25], 'FontSize', 9);

    % 帧滑块
    frame_slider = uicontrol('Parent', control_panel, 'Style', 'slider', ...
        'Min', 1, 'Max', num_frames, 'Value', 1, 'Position', [250, 20, 300, 15]);
    frame_text = uicontrol('Parent', control_panel, 'Style', 'text', ...
        'String', sprintf('Frame: 1/%d', num_frames), 'Position', [560, 15, 80, 25], 'FontSize', 9);

    % 速度解模糊开关
    deambig_check = uicontrol('Parent', control_panel, 'Style', 'checkbox', ...
        'String', 'Velocity Deambig', 'Value', 1, 'Position', [650, 20, 120, 15], 'FontSize', 9);

    % 保存按钮
    save_btn = uicontrol('Parent', control_panel, 'Style', 'pushbutton', ...
        'String', 'Save Results', 'Position', [780, 15, 80, 25], 'FontSize', 9);

    % 创建子图 - 调整位置避免被控制面板遮挡
    % 子图区域从0.02到0.86，为控制面板留出空间
    subplot1 = subplot('Position', [0.08, 0.52, 0.38, 0.32]); % 左上
    subplot2 = subplot('Position', [0.54, 0.52, 0.38, 0.32]); % 右上
    subplot3 = subplot('Position', [0.08, 0.12, 0.38, 0.32]); % 左下
    subplot4 = subplot('Position', [0.54, 0.12, 0.38, 0.32]); % 右下

    fprintf('   ✓ 界面创建完成\n');

    %% 5. 初始化处理变量
    current_frame = 1;
    is_playing = false;
    play_speed = 0.1; % 播放间隔（秒）

    % 存储解模糊结果
    deambiguated_velocities = zeros(num_frames, 1);
    rd_maps1 = cell(num_frames, 1);
    rd_maps2 = cell(num_frames, 1);

    %% 6. 定义回调函数
    set(play_btn, 'Callback', @play_callback);
    set(pause_btn, 'Callback', @pause_callback);
    set(step_btn, 'Callback', @step_callback);
    set(frame_slider, 'Callback', @slider_callback);
    set(save_btn, 'Callback', @save_callback);

    %% 7. 开始处理
    fprintf('5. 开始数据处理...\n');
    fprintf('   使用界面控制播放，或等待自动处理完成\n\n');

    % 处理第一帧并显示
    process_and_display_frame(1);

    %% 内部函数定义
    function play_callback(~, ~)
        is_playing = true;
        set(play_btn, 'Enable', 'off');
        set(pause_btn, 'Enable', 'on');

        while is_playing && current_frame <= num_frames
            process_and_display_frame(current_frame);
            current_frame = current_frame + 1;
            set(frame_slider, 'Value', current_frame);
            pause(play_speed);
        end

        if current_frame > num_frames
            current_frame = 1;
            is_playing = false;
        end

        set(play_btn, 'Enable', 'on');
        set(pause_btn, 'Enable', 'off');
    end

    function pause_callback(~, ~)
        is_playing = false;
        set(play_btn, 'Enable', 'on');
        set(pause_btn, 'Enable', 'off');
    end

    function step_callback(~, ~)
        if current_frame <= num_frames
            process_and_display_frame(current_frame);
            current_frame = current_frame + 1;
            set(frame_slider, 'Value', min(current_frame, num_frames));
        end
    end

    function slider_callback(~, ~)
        current_frame = round(get(frame_slider, 'Value'));
        process_and_display_frame(current_frame);
    end

    function save_callback(~, ~)
        save_results();
    end

    %% 核心处理函数
    function process_and_display_frame(frame_idx)
        if frame_idx > num_frames
            return;
        end

        % 更新帧显示
        set(frame_text, 'String', sprintf('Frame: %d/%d', frame_idx, num_frames));

        % 提取当前帧数据
        frame_data1 = squeeze(subframe1_data(frame_idx, :, :, :));
        frame_data2 = squeeze(subframe2_data(frame_idx, :, :, :));

        % 生成RD图
        [rd_map1, range_bins1, vel_bins1] = generate_rd_map(frame_data1, params1, c);
        [rd_map2, range_bins2, vel_bins2] = generate_rd_map(frame_data2, params2, c);

        % 存储RD图
        rd_maps1{frame_idx} = rd_map1;
        rd_maps2{frame_idx} = rd_map2;

        % 速度解模糊
        if get(deambig_check, 'Value')
            deambig_vel = velocity_deambiguation(rd_map1, rd_map2, vel_bins1, vel_bins2, params1, params2, c);
            deambiguated_velocities(frame_idx) = deambig_vel;
        end

        % 显示结果
        display_rd_maps(rd_map1, rd_map2, range_bins1, range_bins2, vel_bins1, vel_bins2, frame_idx);
    end

    function [rd_map, range_bins, vel_bins] = generate_rd_map(frame_data, params, c)
        % 生成Range-Doppler图
        % 输入: frame_data [chirps, samples, rx], params, c
        % 输出: rd_map [vel_bins, range_bins], range_bins, vel_bins

        fprintf('   处理数据维度: [%d, %d, %d] (chirps, samples, rx)\n', size(frame_data));

        [num_chirps, num_samples, num_rx] = size(frame_data);

        % 距离FFT (对samples维度，即第2维)
        % 应用汉明窗到距离维度
        range_window = repmat(hamming(num_samples)', [num_chirps, 1, num_rx]);
        windowed_data = frame_data .* range_window;

        % 距离FFT - 对第2维(samples)进行FFT
        range_fft_size = 512;
        range_fft = fft(windowed_data, range_fft_size, 2);
        range_fft = fftshift(range_fft, 2);
        range_fft = range_fft(:, 1:range_fft_size/2, :); % 只取正频率部分

        % 速度FFT (对chirps维度，即第1维)
        % 应用汉明窗到速度维度
        vel_window = repmat(hamming(num_chirps), [1, range_fft_size/2, num_rx]);
        windowed_range_fft = range_fft .* vel_window;

        % 速度FFT - 对第1维(chirps)进行FFT
        vel_fft_size = 128;
        vel_fft = fft(windowed_range_fft, vel_fft_size, 1);
%         vel_fft = fftshift(vel_fft, 1); % 速度需要fftshift以获得正负速度

        % 多天线合并 (简单求和)
        rd_map = squeeze(sum(abs(vel_fft).^2, 3));
        rd_map = 10 * log10(rd_map + eps); % 转换为dB

        % 现在rd_map的维度是 [vel_fft_size, range_fft_size/2]
        % 即 [速度bins, 距离bins]

        % 计算坐标轴
        range_res = c / (2 * params.B);
        max_range = range_res * range_fft_size / 2;
        range_bins = linspace(0, max_range, range_fft_size/2);

        vel_res = c / (2 * params.fc * (params.T + params.Tidle) * num_chirps);
        max_vel = vel_res * vel_fft_size / 2;
        vel_bins = linspace(-max_vel, max_vel, vel_fft_size);

        fprintf('   RD图维度: [%d, %d] (速度bins, 距离bins)\n', size(rd_map));
        fprintf('   距离分辨率: %.2f m, 最大距离: %.1f m\n', range_res, max_range);
        fprintf('   速度分辨率: %.2f m/s, 最大速度: %.1f m/s\n', vel_res, max_vel);
    end

    function display_rd_maps(rd_map1, rd_map2, range_bins1, range_bins2, vel_bins1, vel_bins2, frame_idx)
        % 显示RD图
        % rd_map维度: [速度bins, 距离bins]

        % 第一子帧RD图
        axes(subplot1);
        % imagesc(X, Y, C) 其中X是列坐标，Y是行坐标
        % rd_map1的行对应速度，列对应距离
        imagesc(range_bins1, vel_bins1, fliplr(rd_map1));
        axis xy;
        colorbar;
        title(sprintf('Subframe 1 RD Map (Frame %d) - %.1f GHz', frame_idx, params1.fc/1e9), 'FontSize', 10);
        xlabel('Range (m)', 'FontSize', 9);
        ylabel('Velocity (m/s)', 'FontSize', 9);
        colormap(subplot1, jet);
        grid on;

        % 第二子帧RD图
        axes(subplot2);
        imagesc(range_bins2, vel_bins2, fliplr(rd_map2));
        axis xy;
        colorbar;
        title(sprintf('Subframe 2 RD Map (Frame %d) - %.1f GHz', frame_idx, params2.fc/1e9), 'FontSize', 10);
        xlabel('Range (m)', 'FontSize', 9);
        ylabel('Velocity (m/s)', 'FontSize', 9);
        colormap(subplot2, jet);
        grid on;

        % 差异图
        axes(subplot3);
        if size(rd_map1) == size(rd_map2)
            diff_map = rd_map1 - rd_map2;
            imagesc(range_bins1, vel_bins1, fliplr(diff_map));
            axis xy;
            colorbar;
            title(sprintf('Subframe Difference (Frame %d)', frame_idx), 'FontSize', 10);
            xlabel('Range (m)', 'FontSize', 9);
            ylabel('Velocity (m/s)', 'FontSize', 9);
            colormap(subplot3, jet);
            grid on;
        else
            cla;
            text(0.5, 0.5, 'Subframe Size Mismatch', 'HorizontalAlignment', 'center', ...
                'Units', 'normalized', 'FontSize', 12);
            title('Subframe Difference', 'FontSize', 10);
        end

        % 速度解模糊结果
        axes(subplot4);
        if frame_idx > 1
            plot(1:frame_idx, deambiguated_velocities(1:frame_idx), 'b-o', 'LineWidth', 2, 'MarkerSize', 4);
            grid on;
            title('Deambiguated Velocity Time Series', 'FontSize', 10);
            xlabel('Frame Number', 'FontSize', 9);
            ylabel('True Velocity (m/s)', 'FontSize', 9);
            xlim([1, num_frames]);

            % 添加当前值显示
            if ~isnan(deambiguated_velocities(frame_idx))
                text(frame_idx, deambiguated_velocities(frame_idx), ...
                    sprintf('%.2f m/s', deambiguated_velocities(frame_idx)), ...
                    'HorizontalAlignment', 'left', 'VerticalAlignment', 'bottom', ...
                    'FontSize', 8, 'Color', 'red');
            end
        else
            cla;
            text(0.5, 0.5, 'Processing...', 'HorizontalAlignment', 'center', ...
                'Units', 'normalized', 'FontSize', 12);
            title('Deambiguated Velocity Time Series', 'FontSize', 10);
        end

        drawnow;
    end

    function deambig_vel = velocity_deambiguation(rd_map1, rd_map2, vel_bins1, vel_bins2, params1, params2, c)
        % 速度解模糊算法
        % 使用中国剩余定理解决速度模糊问题

        % 找到最强目标的速度
        [~, max_idx1] = max(rd_map1(:));
        [vel_idx1, ~] = ind2sub(size(rd_map1), max_idx1);
        vel_ambig1 = vel_bins1(vel_idx1);

        [~, max_idx2] = max(rd_map2(:));
        [vel_idx2, ~] = ind2sub(size(rd_map2), max_idx2);
        vel_ambig2 = vel_bins2(vel_idx2);

        % 计算速度模糊间隔
        vel_ambig_interval1 = c / (2 * params1.fc * (params1.T + params1.Tidle) * params1.num_chirps);
        vel_ambig_interval2 = c / (2 * params2.fc * (params2.T + params2.Tidle) * params2.num_chirps);

        % 应用中国剩余定理
        deambig_vel = chinese_remainder_theorem(vel_ambig1, vel_ambig2, ...
            vel_ambig_interval1, vel_ambig_interval2);

        % 如果解模糊失败，返回第一子帧的速度
        if isnan(deambig_vel)
            deambig_vel = vel_ambig1;
        end
    end

    function result = chinese_remainder_theorem(v1, v2, T1, T2)
        % 中国剩余定理求解速度解模糊
        % v1, v2: 两个子帧测得的模糊速度
        % T1, T2: 两个子帧的速度模糊间隔

        try
            % 将速度转换为模糊索引
            n1 = round(v1 / T1);
            n2 = round(v2 / T2);

            % 寻找满足条件的真实速度
            max_search_range = 400; % 最大搜索范围 (m/s)

            for k1 = -10:10
                for k2 = -10:10
                    candidate_vel = n1 * T1 + k1 * T1;
                    if abs(candidate_vel - (n2 * T2 + k2 * T2)) < 1 && abs(candidate_vel) < max_search_range
                        result = candidate_vel;
                        return;
                    end
                end
            end

            % 如果没找到匹配，返回NaN
            result = NaN;

        catch
            result = NaN;
        end
    end

    function save_results()
        % 保存处理结果
        try
            timestamp = datestr(now, 'yyyymmdd_HHMMSS');

            % 保存解模糊速度数据
            save(sprintf('deambiguated_velocities_%s.mat', timestamp), ...
                'deambiguated_velocities', 'params1', 'params2');

            % 保存RD图序列（可选）
            if questdlg('是否保存RD图序列？', '保存选项', '是', '否', '否') == "是"
                save(sprintf('rd_maps_%s.mat', timestamp), 'rd_maps1', 'rd_maps2', '-v7.3');
            end

            % 生成处理报告
            generate_report(timestamp);

            fprintf('✓ 结果已保存到文件\n');

        catch ME
            fprintf('❌ 保存失败: %s\n', ME.message);
        end
    end

    function generate_report(timestamp)
        % 生成处理报告
        report_file = sprintf('processing_report_%s.txt', timestamp);
        fid = fopen(report_file, 'w');

        fprintf(fid, '高级帧模式雷达数据处理报告\n');
        fprintf(fid, '生成时间: %s\n\n', datestr(now));

        fprintf(fid, '数据信息:\n');
        fprintf(fid, '  总帧数: %d\n', num_frames);
        fprintf(fid, '  第一子帧chirps: %d\n', params1.num_chirps);
        fprintf(fid, '  第二子帧chirps: %d\n', params2.num_chirps);

        fprintf(fid, '\n第一子帧参数:\n');
        fprintf(fid, '  载频: %.2f GHz\n', params1.fc/1e9);
        fprintf(fid, '  带宽: %.1f MHz\n', params1.B/1e6);
        fprintf(fid, '  距离分辨率: %.2f m\n', range_res1);
        fprintf(fid, '  速度分辨率: %.2f m/s\n', vel_res1);

        fprintf(fid, '\n第二子帧参数:\n');
        fprintf(fid, '  载频: %.2f GHz\n', params2.fc/1e9);
        fprintf(fid, '  带宽: %.1f MHz\n', params2.B/1e6);
        fprintf(fid, '  距离分辨率: %.2f m\n', range_res2);
        fprintf(fid, '  速度分辨率: %.2f m/s\n', vel_res2);

        fprintf(fid, '\n解模糊结果统计:\n');
        valid_vels = deambiguated_velocities(~isnan(deambiguated_velocities));
        if ~isempty(valid_vels)
            fprintf(fid, '  平均速度: %.2f m/s\n', mean(valid_vels));
            fprintf(fid, '  速度标准差: %.2f m/s\n', std(valid_vels));
            fprintf(fid, '  最大速度: %.2f m/s\n', max(valid_vels));
            fprintf(fid, '  最小速度: %.2f m/s\n', min(valid_vels));
        end

        fclose(fid);
    end
end

%% 辅助函数
function [range_res, vel_res, max_range, max_vel] = calculate_resolution(params, c)
    % 计算分辨率参数
    range_res = c / (2 * params.B);
    max_range = range_res * 256; % 假设FFT大小为512，取一半

    vel_res = c / (2 * params.fc * (params.T + params.Tidle) * params.num_chirps);
    max_vel = vel_res * 64; % 假设速度FFT大小为128，取一半
end
