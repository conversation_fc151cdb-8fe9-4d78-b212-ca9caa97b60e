% =========================================================================
%           高尔夫球场雷达监控系统3D可视化 + 动态球轨迹仿真 (简化版)
% =========================================================================
%
% 功能:
% 1. 创建高尔夫球场3D布局，包含n个击球区域（可扩展至10个）
% 2. 在球场后方安装3个MIMO雷达系统（左、中、右）
% 3. 在球场中段安装子雷达监控系统
% 4. 简化的雷达覆盖区域指示
% 5. 动态仿真高尔夫球发射轨迹
% 6. 实时显示雷达监控效果
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

% --- 高尔夫球场布局参数 ---
n = 5;                      % 击球区域数量 (可扩展至10个)
tee_spacing = 10;           % 击球区域之间的间距 (m)
course_length = 250;        % 球场总长度 (m)
course_width = 60;          % 球场总宽度 (m)
tee_area_size = 8;          % 每个击球区域的大小 (m)

% --- 主雷达系统参数（后端） ---
main_radar_count = 3;       % 主雷达数量（左、中、右）
main_radar_height = 15;     % 主雷达安装高度 (m)
main_radar_distance = -20;  % 主雷达距离球场起点的距离 (m，负数表示在后方)
main_radar_spacing = 25;    % 主雷达之间的间距 (m)
main_radar_tilt = -15;      % 主雷达俯视角度 (度)
main_radar_inward_angle = 10; % 左右雷达向内倾斜角度 (度)

% --- 子雷达系统参数（中段监控） ---
sub_radar_distance = 50;    % 子雷达距离起点的距离 (m)
sub_radar_height = 8;       % 子雷达安装高度 (m)
sub_radar_coverage = 5;     % 每个子雷达覆盖的击球区域数量
sub_radar_count = ceil(n / sub_radar_coverage); % 子雷达数量

% --- 雷达覆盖区域参数 ---
main_radar_range = 200;     % 主雷达最大探测距离 (m)
sub_radar_range = 80;       % 子雷达最大探测距离 (m)

% --- 高尔夫球物理参数 ---
min_v0 = 36;            % 最小初始速度 (m/s)
max_v0 = 100;           % 最大初始速度 (m/s)
min_angle = 9;          % 最小发射仰角 (度)
max_angle = 14;         % 最大发射仰角 (度)
min_azimuth = -3.0;     % 最小水平偏角 (度)，负数代表偏左
max_azimuth = 3.0;      % 最大水平偏角 (度)，正数代表偏右
max_launch_delay = 2.0; % 最大发射延迟时间 (s)
dt = 0.01;              % 时间步长 (s)

% --- 物理常量 ---
g = 9.81;               % 重力加速度 (m/s^2)
m = 0.0459;             % 高尔夫球质量 (kg)
r = 0.02135;            % 高尔夫球半径 (m)
A = pi * r^2;           % 高尔夫球横截面积 (m^2)
rho = 1.225;            % 空气密度 (kg/m^3)
Cd = 0.4;               % 阻力系数 (对于球体是典型值)

% --- 可视化参数 ---
radar_size = [2, 1.5, 1];   % 雷达设备尺寸 [长, 宽, 高] (m)

%% 2. 创建高尔夫球场3D布局
fprintf('正在创建高尔夫球场3D布局...\n');

% 计算击球区域位置
tee_positions = zeros(n, 3);
for i = 1:n
    tee_positions(i, :) = [(i-1) * tee_spacing - (n-1)*tee_spacing/2, 0, 0];
end

% 计算主雷达位置
main_radar_positions = zeros(main_radar_count, 3);
for i = 1:main_radar_count
    x_offset = (i-2) * main_radar_spacing; % 中心雷达在x=0，左右分布
    main_radar_positions(i, :) = [x_offset, main_radar_distance, main_radar_height];
end

% 计算子雷达位置
sub_radar_positions = zeros(sub_radar_count, 3);
for i = 1:sub_radar_count
    % 子雷达沿击球区域分布
    coverage_center = (i-1) * sub_radar_coverage + sub_radar_coverage/2;
    if coverage_center > n
        coverage_center = n - sub_radar_coverage/2;
    end
    x_pos = (coverage_center - 1) * tee_spacing - (n-1)*tee_spacing/2;
    sub_radar_positions(i, :) = [x_pos, sub_radar_distance, sub_radar_height];
end

fprintf('击球区域数量: %d\n', n);
fprintf('主雷达数量: %d\n', main_radar_count);
fprintf('子雷达数量: %d\n', sub_radar_count);
fprintf('球场布局创建完成。\n\n');

%% 3. 初始化高尔夫球参数
fprintf('正在初始化高尔夫球参数...\n');

% 预分配结构体数组以提高效率
balls(n) = struct('id', [], 'pos', [], 'vel', [], 'path', [], ...
                  'launch_time', [], 'launch_angle', [], 'launch_speed', [], 'azimuth', [], ...
                  'is_active', false, 'has_launched', false);

% 为每个球随机生成参数
fprintf('----------- 高尔夫球初始参数 -----------\n');
for i = 1:n
    % --- 基本参数 ---
    balls(i).id = i;

    % --- 随机参数 ---
    % 垂直发射角 (仰角)
    elevation_deg = min_angle + (max_angle - min_angle) * rand();
    elevation_rad = deg2rad(elevation_deg);
    balls(i).launch_angle = elevation_deg;

    % 水平发射角 (偏角/方位角)
    azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();
    azimuth_rad = deg2rad(azimuth_deg);
    balls(i).azimuth = azimuth_deg;

    balls(i).launch_time = max_launch_delay * rand();

    % 为每个球生成一个在指定范围内的随机速度
    v0_rand = min_v0 + (max_v0 - min_v0) * rand();
    balls(i).launch_speed = v0_rand;

    % --- 物理状态 ---
    balls(i).pos = [tee_positions(i, 1); tee_positions(i, 2); 1.0];

    % 使用三角函数将速度分解到X, Y, Z三个轴
    % 首先，将速度投影到水平面(v_xy)和垂直面(v_z)
    v_xy = v0_rand * cos(elevation_rad);
    v_z = v0_rand * sin(elevation_rad);

    % 然后，根据水平偏角(azimuth)将v_xy分解到vx和vy
    v_x = v_xy * sin(azimuth_rad);
    v_y = v_xy * cos(azimuth_rad);

    balls(i).vel = [v_x; v_y; v_z];

    % --- 初始化轨迹和状态标志 ---
    balls(i).path = []; % 轨迹历史初始化为空
    balls(i).is_active = false; % 初始状态为"未在空中"
    balls(i).has_launched = false; % 初始状态为"尚未发射"

    % --- 打印信息 ---
    fprintf('球 %d: 时间=%.2fs, 速度=%.2fm/s, 仰角=%.2f°, 偏角=%.2f°\n', ...
            i, balls(i).launch_time, balls(i).launch_speed, balls(i).launch_angle, balls(i).azimuth);
end
fprintf('------------------------------------------\n\n');

%% 4. 创建3D可视化环境
figure('Name', '高尔夫球场雷达监控系统 + 动态轨迹仿真 (简化版)', 'NumberTitle', 'off', ...
       'Color', 'w', 'Position', [50, 50, 1400, 900]);
hold on;
grid on;
box on;

% 设置坐标轴
xlabel('X 轴 - 横向位置 (m)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Y 轴 - 纵向距离 (m)', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Z 轴 - 高度 (m)', 'FontSize', 12, 'FontWeight', 'bold');

% 设置视角和比例
view(45, 20);
axis equal;
set(gca, 'FontSize', 10);

% 设置坐标轴范围
x_range = [-course_width/2, course_width/2];
y_range = [main_radar_distance-10, course_length];
z_range = [0, main_radar_height+5];
xlim(x_range);
ylim(y_range);
zlim(z_range);

%% 5. 绘制高尔夫球场基础设施
fprintf('正在绘制球场基础设施...\n');

% 绘制球场地面
[X_ground, Y_ground] = meshgrid(x_range(1):5:x_range(2), y_range(1):5:y_range(2));
Z_ground = zeros(size(X_ground));
surf(X_ground, Y_ground, Z_ground, 'FaceColor', [0.2, 0.8, 0.2], ...
     'FaceAlpha', 0.3, 'EdgeColor', 'none');

% 绘制击球区域
for i = 1:n
    pos = tee_positions(i, :);
    % 创建击球区域的方形平台
    x_tee = pos(1) + [-tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2, -tee_area_size/2];
    y_tee = pos(2) + [-tee_area_size/2, -tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2];
    z_tee = [0.1, 0.1, 0.1, 0.1, 0.1];

    plot3(x_tee, y_tee, z_tee, 'k-', 'LineWidth', 2);
    fill3(x_tee(1:4), y_tee(1:4), z_tee(1:4), [0.8, 0.6, 0.4], 'FaceAlpha', 0.7);

    % 添加击球区域标签
    text(pos(1), pos(2), pos(3)+2, sprintf('击球区 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
end

fprintf('球场基础设施绘制完成。\n');

%% 6. 绘制雷达系统
fprintf('正在绘制雷达系统...\n');

main_radar_colors = [1, 0, 0; 0, 0, 1; 0, 1, 0]; % 红、蓝、绿
main_radar_names = {'左侧主雷达', '中央主雷达', '右侧主雷达'};
sub_radar_color = [1, 0.5, 0]; % 橙色

% 绘制主雷达系统
for i = 1:main_radar_count
    pos = main_radar_positions(i, :);
    color = main_radar_colors(i, :);

    % 绘制雷达设备本体
    draw_radar_box(pos, radar_size, color, 0.8);

    % 添加雷达标签
    text(pos(1), pos(2), pos(3)+radar_size(3)+1, main_radar_names{i}, ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold', ...
         'Color', color);

    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 3);

    % 计算雷达朝向角度并绘制覆盖区域指示线
    if i == 1  % 左侧雷达，向右倾斜
        azimuth_offset = main_radar_inward_angle;
    elseif i == 3  % 右侧雷达，向左倾斜
        azimuth_offset = -main_radar_inward_angle;
    else  % 中央雷达，直视前方
        azimuth_offset = 0;
    end

    % 绘制简化的覆盖区域指示线
    end_x = pos(1) + main_radar_range * 0.5 * sind(azimuth_offset);
    end_y = pos(2) + main_radar_range * 0.5 * cosd(main_radar_tilt);
    end_z = pos(3) + main_radar_range * 0.5 * sind(main_radar_tilt);

    plot3([pos(1), end_x], [pos(2), end_y], [pos(3), end_z], ...
          'Color', color, 'LineWidth', 2, 'LineStyle', '--');
end

% 绘制子雷达系统
for i = 1:sub_radar_count
    pos = sub_radar_positions(i, :);

    % 绘制子雷达设备本体
    sub_size = radar_size * 0.7;
    draw_radar_box(pos, sub_size, sub_radar_color, 0.8);

    % 添加子雷达标签
    text(pos(1), pos(2), pos(3)+sub_size(3)+1, sprintf('子雷达 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold', ...
         'Color', sub_radar_color);

    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 2);

    % 绘制子雷达指向线
    end_y = pos(2) + sub_radar_range * 0.5 * cosd(-10);
    end_z = pos(3) + sub_radar_range * 0.5 * sind(-10);

    plot3([pos(1), pos(1)], [pos(2), end_y], [pos(3), end_z], ...
          'Color', sub_radar_color, 'LineWidth', 1.5, 'LineStyle', '--');
end

fprintf('雷达系统绘制完成。\n');

%% 7. 初始化球轨迹绘图对象
fprintf('正在初始化轨迹绘图对象...\n');

% 为每个球的路径和当前位置创建绘图句柄
path_plots = gobjects(1, n);
ball_markers = gobjects(1, n);
colors = lines(n); % 为每个球生成不同的颜色

for i = 1:n
    % 路径线条 (初始为空)
    path_plots(i) = plot3(NaN, NaN, NaN, '-', 'Color', colors(i,:), 'LineWidth', 2);
    % 球的当前位置标记
    ball_markers(i) = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', colors(i,:), ...
                            'MarkerEdgeColor', 'k', 'MarkerSize', 8);
end

fprintf('轨迹绘图对象初始化完成。\n');

%% 8. 添加图例和标题
title('高尔夫球场雷达监控系统 + 动态轨迹仿真 (简化版)', 'FontSize', 16, 'FontWeight', 'bold');

% 创建图例
legend_handles = [];
legend_labels = {};

% 添加主雷达图例
for i = 1:main_radar_count
    h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', main_radar_colors(i, :), ...
              'MarkerEdgeColor', 'k', 'MarkerSize', 10);
    legend_handles(end+1) = h;
    legend_labels{end+1} = main_radar_names{i};
end

% 添加子雷达图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', sub_radar_color, ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '子雷达系统';

% 添加击球区域图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', [0.8, 0.6, 0.4], ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '击球区域';

% 添加球轨迹图例
h = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', [0.5, 0.5, 0.5], ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 6);
legend_handles(end+1) = h;
legend_labels{end+1} = '高尔夫球轨迹';

legend(legend_handles, legend_labels, 'Location', 'northeast', 'FontSize', 10);

%% 9. 动态仿真主循环
fprintf('\n正在开始动态轨迹仿真...\n');
fprintf('仿真开始... 按 Ctrl+C 停止。\n');

sim_time = 0;
num_finished_balls = 0; % 完成轨迹的球的数量

while num_finished_balls < n

    % --- 更新每个球的状态 ---
    for i = 1:n
        % 检查是否到了发射时间且尚未发射
        if sim_time >= balls(i).launch_time && ~balls(i).has_launched
            balls(i).has_launched = true;
            balls(i).is_active = true;
            % 将初始位置存入路径历史
            balls(i).path = balls(i).pos;
            fprintf('球 %d 发射！时间: %.2fs\n', i, sim_time);
        end

        % 如果球在空中，则更新其物理状态
        if balls(i).is_active
            % --- 计算物理量 ---
            v_mag = norm(balls(i).vel);
            F_drag = -0.5 * rho * A * Cd * v_mag * balls(i).vel;
            F_net = [0; 0; -m*g] + F_drag;
            a = F_net / m;

            % --- 更新速度和位置 (欧拉法) ---
            balls(i).vel = balls(i).vel + a * dt;
            balls(i).pos = balls(i).pos + balls(i).vel * dt;

            % 记录轨迹点
            balls(i).path = [balls(i).path, balls(i).pos];

            % 检查是否落地
            if balls(i).pos(3) < 0
                balls(i).is_active = false; % 球落地，停止更新
                num_finished_balls = num_finished_balls + 1; % 完成的球数加一
                fprintf('球 %d 落地！飞行时间: %.2fs, 距离: %.1fm\n', ...
                        i, sim_time - balls(i).launch_time, balls(i).pos(2));
            end
        end
    end

    % --- 更新绘图 ---
    for i = 1:n
        if balls(i).has_launched
            % 更新路径线条
            set(path_plots(i), 'XData', balls(i).path(1,:), ...
                               'YData', balls(i).path(2,:), ...
                               'ZData', balls(i).path(3,:));
            % 更新球的当前位置
            if balls(i).is_active
                set(ball_markers(i), 'XData', balls(i).pos(1), ...
                                     'YData', balls(i).pos(2), ...
                                     'ZData', balls(i).pos(3));
            else
                % 球落地后显示最终位置
                final_pos = balls(i).path(:, end);
                final_pos(3) = 0; % 确保在地面上
                set(ball_markers(i), 'XData', final_pos(1), ...
                                     'YData', final_pos(2), ...
                                     'ZData', final_pos(3));
            end
        end
    end

    % 刷新画布以显示更新
    drawnow limitrate;

    % 更新仿真时间
    sim_time = sim_time + dt;

    % 防止无限循环
    if sim_time > 30 % 30秒超时
        fprintf('仿真超时，强制结束。\n');
        break;
    end
end

% 调整最终视图以显示所有轨迹
axis tight; % 自动调整坐标轴范围以适应所有数据

fprintf('\n=== 仿真完成 ===\n');
fprintf('- 击球区域: %d 个\n', n);
fprintf('- 主雷达系统: %d 个\n', main_radar_count);
fprintf('- 子雷达系统: %d 个\n', sub_radar_count);
fprintf('- 完成轨迹的球: %d 个\n', num_finished_balls);
fprintf('- 总仿真时间: %.2f 秒\n', sim_time);
fprintf('请旋转视角查看完整的3D布局和轨迹。\n');

%% 辅助函数定义区域

% 辅助函数：绘制雷达设备长方体
function draw_radar_box(center, size, color, alpha)
    % center: [x, y, z] 中心位置
    % size: [length, width, height] 尺寸
    % color: [r, g, b] 颜色
    % alpha: 透明度

    x = center(1) + [-size(1)/2, size(1)/2];
    y = center(2) + [-size(2)/2, size(2)/2];
    z = center(3) + [-size(3)/2, size(3)/2];

    % 定义长方体的8个顶点
    vertices = [
        x(1), y(1), z(1);  % 1
        x(2), y(1), z(1);  % 2
        x(2), y(2), z(1);  % 3
        x(1), y(2), z(1);  % 4
        x(1), y(1), z(2);  % 5
        x(2), y(1), z(2);  % 6
        x(2), y(2), z(2);  % 7
        x(1), y(2), z(2);  % 8
    ];

    % 定义6个面
    faces = [
        1, 2, 3, 4;  % 底面
        5, 6, 7, 8;  % 顶面
        1, 2, 6, 5;  % 前面
        3, 4, 8, 7;  % 后面
        1, 4, 8, 5;  % 左面
        2, 3, 7, 6;  % 右面
    ];

    % 绘制长方体
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', alpha, ...
          'EdgeColor', 'k', 'LineWidth', 1);
end
