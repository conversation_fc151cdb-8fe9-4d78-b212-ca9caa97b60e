% =========================================================================
%                   高尔夫球场雷达监控系统3D可视化 (简化版)
% =========================================================================
%
% 功能:
% 1. 创建高尔夫球场3D布局，包含n个击球区域（可扩展至10个）
% 2. 在球场后方安装3个MIMO雷达系统（左、中、右）
% 3. 在球场中段安装子雷达监控系统
% 4. 可视化雷达覆盖区域和监控范围
% 5. 提供完整的3D沉浸式可视化体验
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

% --- 高尔夫球场布局参数 ---
n = 5;                      % 击球区域数量 (可扩展至10个)
tee_spacing = 10;           % 击球区域之间的间距 (m)
course_length = 250;        % 球场总长度 (m)
course_width = 60;          % 球场总宽度 (m)
tee_area_size = 8;          % 每个击球区域的大小 (m)

% --- 主雷达系统参数（后端） ---
main_radar_count = 3;       % 主雷达数量（左、中、右）
main_radar_height = 15;     % 主雷达安装高度 (m)
main_radar_distance = -20;  % 主雷达距离球场起点的距离 (m，负数表示在后方)
main_radar_spacing = 25;    % 主雷达之间的间距 (m)
main_radar_tilt = -15;      % 主雷达俯视角度 (度)
main_radar_inward_angle = 10; % 左右雷达向内倾斜角度 (度)

% --- 子雷达系统参数（中段监控） ---
sub_radar_distance = 50;    % 子雷达距离起点的距离 (m)
sub_radar_height = 8;       % 子雷达安装高度 (m)
sub_radar_coverage = 5;     % 每个子雷达覆盖的击球区域数量
sub_radar_count = ceil(n / sub_radar_coverage); % 子雷达数量

% --- 雷达覆盖区域参数 ---
main_radar_range = 200;     % 主雷达最大探测距离 (m)
main_radar_fov_h = 60;      % 主雷达水平视场角 (度)
main_radar_fov_v = 30;      % 主雷达垂直视场角 (度)
sub_radar_range = 80;       % 子雷达最大探测距离 (m)
sub_radar_fov_h = 45;       % 子雷达水平视场角 (度)
sub_radar_fov_v = 25;       % 子雷达垂直视场角 (度)

% --- 可视化参数 ---
coverage_alpha = 0.15;      % 覆盖区域透明度
radar_size = [2, 1.5, 1];   % 雷达设备尺寸 [长, 宽, 高] (m)

%% 2. 创建高尔夫球场3D布局
fprintf('正在创建高尔夫球场3D布局...\n');

% 计算击球区域位置
tee_positions = zeros(n, 3);
for i = 1:n
    tee_positions(i, :) = [(i-1) * tee_spacing - (n-1)*tee_spacing/2, 0, 0];
end

% 计算主雷达位置
main_radar_positions = zeros(main_radar_count, 3);
for i = 1:main_radar_count
    x_offset = (i-2) * main_radar_spacing; % 中心雷达在x=0，左右分布
    main_radar_positions(i, :) = [x_offset, main_radar_distance, main_radar_height];
end

% 计算子雷达位置
sub_radar_positions = zeros(sub_radar_count, 3);
for i = 1:sub_radar_count
    % 子雷达沿击球区域分布
    coverage_center = (i-1) * sub_radar_coverage + sub_radar_coverage/2;
    if coverage_center > n
        coverage_center = n - sub_radar_coverage/2;
    end
    x_pos = (coverage_center - 1) * tee_spacing - (n-1)*tee_spacing/2;
    sub_radar_positions(i, :) = [x_pos, sub_radar_distance, sub_radar_height];
end

fprintf('击球区域数量: %d\n', n);
fprintf('主雷达数量: %d\n', main_radar_count);
fprintf('子雷达数量: %d\n', sub_radar_count);
fprintf('球场布局创建完成。\n\n');

%% 3. 创建3D可视化环境
figure('Name', '高尔夫球场雷达监控系统3D可视化', 'NumberTitle', 'off', ...
       'Color', 'w', 'Position', [100, 100, 1200, 800]);
hold on;
grid on;
box on;

% 设置坐标轴
xlabel('X 轴 - 横向位置 (m)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Y 轴 - 纵向距离 (m)', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Z 轴 - 高度 (m)', 'FontSize', 12, 'FontWeight', 'bold');

% 设置视角和比例
view(45, 20);
axis equal;
set(gca, 'FontSize', 10);

% 设置坐标轴范围
x_range = [-course_width/2, course_width/2];
y_range = [main_radar_distance-10, course_length];
z_range = [0, main_radar_height+5];
xlim(x_range);
ylim(y_range);
zlim(z_range);

%% 4. 绘制高尔夫球场基础设施
fprintf('正在绘制球场基础设施...\n');

% 绘制球场地面
[X_ground, Y_ground] = meshgrid(x_range(1):5:x_range(2), y_range(1):5:y_range(2));
Z_ground = zeros(size(X_ground));
surf(X_ground, Y_ground, Z_ground, 'FaceColor', [0.2, 0.8, 0.2], ...
     'FaceAlpha', 0.3, 'EdgeColor', 'none');

% 绘制击球区域
for i = 1:n
    pos = tee_positions(i, :);
    % 创建击球区域的方形平台
    x_tee = pos(1) + [-tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2, -tee_area_size/2];
    y_tee = pos(2) + [-tee_area_size/2, -tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2];
    z_tee = [0.1, 0.1, 0.1, 0.1, 0.1];
    
    plot3(x_tee, y_tee, z_tee, 'k-', 'LineWidth', 2);
    fill3(x_tee(1:4), y_tee(1:4), z_tee(1:4), [0.8, 0.6, 0.4], 'FaceAlpha', 0.7);
    
    % 添加击球区域标签
    text(pos(1), pos(2), pos(3)+2, sprintf('击球区 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
end

fprintf('球场基础设施绘制完成。\n');

%% 5. 绘制主雷达系统
fprintf('正在绘制主雷达系统...\n');

main_radar_colors = [1, 0, 0; 0, 0, 1; 0, 1, 0]; % 红、蓝、绿
main_radar_names = {'左侧主雷达', '中央主雷达', '右侧主雷达'};

for i = 1:main_radar_count
    pos = main_radar_positions(i, :);
    color = main_radar_colors(i, :);
    
    % 绘制雷达设备本体（长方体）
    draw_radar_box(pos, radar_size, color, 0.8);
    
    % 添加雷达标签
    text(pos(1), pos(2), pos(3)+radar_size(3)+1, main_radar_names{i}, ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold', ...
         'Color', color);
    
    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 3);
    
    % 绘制简化的覆盖区域指示线
    if i == 1  % 左侧雷达，向右倾斜
        azimuth_offset = main_radar_inward_angle;
    elseif i == 3  % 右侧雷达，向左倾斜
        azimuth_offset = -main_radar_inward_angle;
    else  % 中央雷达，直视前方
        azimuth_offset = 0;
    end
    
    % 绘制雷达指向线
    end_x = pos(1) + main_radar_range * sind(azimuth_offset);
    end_y = pos(2) + main_radar_range * cosd(main_radar_tilt);
    end_z = pos(3) + main_radar_range * sind(main_radar_tilt);
    
    plot3([pos(1), end_x], [pos(2), end_y], [pos(3), end_z], ...
          'Color', color, 'LineWidth', 2, 'LineStyle', '--');
end

fprintf('主雷达系统绘制完成。\n');

%% 6. 绘制子雷达系统
fprintf('正在绘制子雷达系统...\n');

sub_radar_color = [1, 0.5, 0]; % 橙色

for i = 1:sub_radar_count
    pos = sub_radar_positions(i, :);
    
    % 绘制子雷达设备本体（较小的长方体）
    sub_size = radar_size * 0.7; % 子雷达比主雷达小30%
    draw_radar_box(pos, sub_size, sub_radar_color, 0.8);
    
    % 添加子雷达标签
    text(pos(1), pos(2), pos(3)+sub_size(3)+1, sprintf('子雷达 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold', ...
         'Color', sub_radar_color);
    
    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 2);
    
    % 绘制子雷达指向线
    end_y = pos(2) + sub_radar_range * cosd(-10);
    end_z = pos(3) + sub_radar_range * sind(-10);
    
    plot3([pos(1), pos(1)], [pos(2), end_y], [pos(3), end_z], ...
          'Color', sub_radar_color, 'LineWidth', 1.5, 'LineStyle', '--');
end

fprintf('子雷达系统绘制完成。\n');

%% 7. 添加图例和标题
title('高尔夫球场雷达监控系统3D布局', 'FontSize', 16, 'FontWeight', 'bold');

% 创建图例
legend_handles = [];
legend_labels = {};

% 添加主雷达图例
for i = 1:main_radar_count
    h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', main_radar_colors(i, :), ...
              'MarkerEdgeColor', 'k', 'MarkerSize', 10);
    legend_handles(end+1) = h;
    legend_labels{end+1} = main_radar_names{i};
end

% 添加子雷达图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', sub_radar_color, ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '子雷达系统';

% 添加击球区域图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', [0.8, 0.6, 0.4], ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '击球区域';

legend(legend_handles, legend_labels, 'Location', 'northeast', 'FontSize', 10);

fprintf('\n=== 高尔夫球场雷达监控系统3D可视化完成 ===\n');
fprintf('- 击球区域: %d 个\n', n);
fprintf('- 主雷达系统: %d 个\n', main_radar_count);
fprintf('- 子雷达系统: %d 个\n', sub_radar_count);
fprintf('请旋转视角查看完整的3D布局。\n');

%% 辅助函数定义区域

% 辅助函数：绘制雷达设备长方体
function draw_radar_box(center, size, color, alpha)
    % center: [x, y, z] 中心位置
    % size: [length, width, height] 尺寸
    % color: [r, g, b] 颜色
    % alpha: 透明度
    
    x = center(1) + [-size(1)/2, size(1)/2];
    y = center(2) + [-size(2)/2, size(2)/2];
    z = center(3) + [-size(3)/2, size(3)/2];
    
    % 定义长方体的8个顶点
    vertices = [
        x(1), y(1), z(1);  % 1
        x(2), y(1), z(1);  % 2
        x(2), y(2), z(1);  % 3
        x(1), y(2), z(1);  % 4
        x(1), y(1), z(2);  % 5
        x(2), y(1), z(2);  % 6
        x(2), y(2), z(2);  % 7
        x(1), y(2), z(2);  % 8
    ];
    
    % 定义6个面
    faces = [
        1, 2, 3, 4;  % 底面
        5, 6, 7, 8;  % 顶面
        1, 2, 6, 5;  % 前面
        3, 4, 8, 7;  % 后面
        1, 4, 8, 5;  % 左面
        2, 3, 7, 6;  % 右面
    ];
    
    % 绘制长方体
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', alpha, ...
          'EdgeColor', 'k', 'LineWidth', 1);
end
