%% RD图维度检查和修复脚本
% 用于验证Range-Doppler图的维度是否正确

function check_rd_dimensions()
    clear; clc; close all;
    
    fprintf('=== RD图维度检查和修复 ===\n\n');
    
    %% 1. 设置字体支持
    try
        if ispc
            set(0, 'DefaultAxesFontName', 'SimHei');
            set(0, 'DefaultTextFontName', 'SimHei');
            set(0, 'DefaultUicontrolFontName', 'SimHei');
        elseif ismac
            set(0, 'DefaultAxesFontName', 'PingFang SC');
            set(0, 'DefaultTextFontName', 'PingFang SC');
            set(0, 'DefaultUicontrolFontName', 'PingFang SC');
        end
        fprintf('✓ 字体设置完成\n');
    catch
        fprintf('⚠ 字体设置失败，可能显示为方框\n');
    end
    
    %% 2. 加载测试数据
    fprintf('\n2. 加载测试数据...\n');
    try
        if ~exist('data_reduced_precision_advanced_frame.mat', 'file')
            error('数据文件不存在，请先运行数据生成程序');
        end
        
        data = load('data_reduced_precision_advanced_frame.mat');
        RawDataMatrix = data.RawDataMatrix;
        fmcw = data.fmcw;
        
        fprintf('   数据维度: [%d, %d, %d, %d]\n', size(RawDataMatrix));
        fprintf('   (帧数, chirps, 采样点, 接收天线)\n');
        
    catch ME
        fprintf('❌ 数据加载失败: %s\n', ME.message);
        return;
    end
    
    %% 3. 分离子帧数据
    fprintf('\n3. 分离子帧数据...\n');
    
    subframe1_chirps = fmcw.NumOfChirpInOneFrame;
    subframe2_chirps = fmcw.NumOfChirpInOneSubframe2;
    
    subframe1_data = RawDataMatrix(:, 1:subframe1_chirps, :, :);
    subframe2_data = RawDataMatrix(:, (subframe1_chirps+1):end, :, :);
    
    fprintf('   第一子帧数据: [%d, %d, %d, %d]\n', size(subframe1_data));
    fprintf('   第二子帧数据: [%d, %d, %d, %d]\n', size(subframe2_data));
    
    %% 4. 测试RD图生成
    fprintf('\n4. 测试RD图生成...\n');
    
    % 取第一帧第一子帧数据
    test_frame = squeeze(subframe1_data(1, :, :, :));
    fprintf('   测试帧维度: [%d, %d, %d] (chirps, samples, rx)\n', size(test_frame));
    
    [num_chirps, num_samples, num_rx] = size(test_frame);
    
    % 距离FFT
    range_fft_size = 512;
    range_fft = fft(test_frame, range_fft_size, 2); % 对第2维(samples)进行FFT
    range_fft = range_fft(:, 1:range_fft_size/2, :);
    fprintf('   距离FFT后维度: [%d, %d, %d]\n', size(range_fft));
    
    % 速度FFT
    vel_fft_size = 128;
    vel_fft = fft(range_fft, vel_fft_size, 1); % 对第1维(chirps)进行FFT
    vel_fft = fftshift(vel_fft, 1);
    fprintf('   速度FFT后维度: [%d, %d, %d]\n', size(vel_fft));
    
    % 生成RD图
    rd_map = squeeze(sum(abs(vel_fft).^2, 3));
    rd_map_db = 10 * log10(rd_map + eps);
    fprintf('   RD图维度: [%d, %d] (速度bins, 距离bins)\n', size(rd_map_db));
    
    %% 5. 计算坐标轴
    fprintf('\n5. 计算坐标轴...\n');
    
    c = fmcw.propagationSpeed;
    
    % 距离轴
    range_res = c / (2 * fmcw.Bw);
    max_range = range_res * range_fft_size / 2;
    range_bins = linspace(0, max_range, range_fft_size/2);
    
    % 速度轴
    vel_res = c / (2 * fmcw.radarFrequency * (fmcw.chirpTRandEndime + fmcw.Tidle) * num_chirps);
    max_vel = vel_res * vel_fft_size / 2;
    vel_bins = linspace(-max_vel, max_vel, vel_fft_size);
    
    fprintf('   距离分辨率: %.2f m, 最大距离: %.1f m\n', range_res, max_range);
    fprintf('   速度分辨率: %.2f m/s, 最大速度: %.1f m/s\n', vel_res, max_vel);
    fprintf('   距离bins数量: %d\n', length(range_bins));
    fprintf('   速度bins数量: %d\n', length(vel_bins));
    
    %% 6. 可视化测试
    fprintf('\n6. 可视化测试...\n');
    
    figure('Name', 'RD图维度验证', 'Position', [100, 100, 1200, 800]);
    
    % 方法1：正确的维度显示
    subplot(2, 2, 1);
    imagesc(range_bins, vel_bins, rd_map_db);
    axis xy;
    colorbar;
    title('正确显示: Range-Doppler Map', 'FontSize', 12);
    xlabel('Distance (m)', 'FontSize', 10);
    ylabel('Velocity (m/s)', 'FontSize', 10);
    grid on;
    
    % 方法2：错误的维度显示（对比）
    subplot(2, 2, 2);
    imagesc(vel_bins, range_bins, rd_map_db');
    axis xy;
    colorbar;
    title('错误显示: 维度颠倒', 'FontSize', 12);
    xlabel('Velocity (m/s)', 'FontSize', 10);
    ylabel('Distance (m)', 'FontSize', 10);
    grid on;
    
    % 方法3：使用bins索引
    subplot(2, 2, 3);
    imagesc(rd_map_db);
    colorbar;
    title('Bins索引显示', 'FontSize', 12);
    xlabel('Distance bins', 'FontSize', 10);
    ylabel('Velocity bins', 'FontSize', 10);
    grid on;
    
    % 方法4：3D显示
    subplot(2, 2, 4);
    [R, V] = meshgrid(range_bins, vel_bins);
    surf(R, V, rd_map_db, 'EdgeColor', 'none');
    view(45, 30);
    colorbar;
    title('3D RD Map', 'FontSize', 12);
    xlabel('Distance (m)', 'FontSize', 10);
    ylabel('Velocity (m/s)', 'FontSize', 10);
    zlabel('Power (dB)', 'FontSize', 10);
    
    %% 7. 维度验证总结
    fprintf('\n7. 维度验证总结:\n');
    fprintf('   ✓ 数据流向: 原始数据 → 距离FFT → 速度FFT → RD图\n');
    fprintf('   ✓ 维度变化: [chirps,samples,rx] → [chirps,range_bins,rx] → [vel_bins,range_bins,rx] → [vel_bins,range_bins]\n');
    fprintf('   ✓ 正确显示: imagesc(range_bins, vel_bins, rd_map)\n');
    fprintf('   ✓ 坐标轴: X轴=距离, Y轴=速度\n');
    
    %% 8. 性能分析
    fprintf('\n8. 性能分析:\n');
    fprintf('   动态范围: %.1f dB\n', max(rd_map_db(:)) - min(rd_map_db(:)));
    fprintf('   最大功率: %.1f dB\n', max(rd_map_db(:)));
    fprintf('   最小功率: %.1f dB\n', min(rd_map_db(:)));
    
    % 找到最强目标
    [max_val, max_idx] = max(rd_map_db(:));
    [vel_idx, range_idx] = ind2sub(size(rd_map_db), max_idx);
    target_range = range_bins(range_idx);
    target_vel = vel_bins(vel_idx);
    
    fprintf('   最强目标位置: 距离=%.2f m, 速度=%.2f m/s, 功率=%.1f dB\n', ...
        target_range, target_vel, max_val);
    
    fprintf('\n=== 维度检查完成 ===\n');
    fprintf('现在可以运行修复后的雷达处理器\n');
end
