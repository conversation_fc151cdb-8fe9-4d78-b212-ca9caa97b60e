classdef MIMORADAR
    % �����״��豸���������
    % ����:
    % �����Գ�ʼ����
    %       obj = init_RDmap(obj)����ʼ����������գ�RD��ͼ��
    %       obj = ���ɵ�Ƶ����������(obj)����ʼ��FMCW��Ƶ����
    %   s_beatnoisy = ���Ӹ�˹����(s_beat)������˹�������ӵ��ź�s_beat
    %   RDͼ = RDͼ(obj, s_beat)��ͨ��2xFFT��s_beat����RDͼ���Էֱ�Ϊ��λ��

% ����FMCW���
    properties
        chirpShape                  = 'SAWgap';     % ѡ���Ƶ��״: ['TRI', 'SAW1', 'SAWgap']
                                                    % 'TRI'��������״��
                                                    % 'SAW1'����ݲ���״��
                                                    % 'SAWgap'�������ʱ��ľ�ݲ���״��

        Bw                          = 4e9           % ����
        Tidle                       = 0;            % ��ͣʱ��
        sweepBw                     = 1e9;          % ��Ƶб��
        chirpTRandEndime            = 32e-6;        % ��Ƶб��ʱ�� fmin->fmax

        propagationSpeed            = 3e8;          % �������״����ߵĴ����ٶ�
        adcSamplingFrequency        = 10e6;         % ADC����Ƶ��
        numadcInPerChirp            = 32;           % ADC��������
        radarFrequency              = 76.5e9;       % �״�Ƶ��
        chirpsPerCycle              = 256;          % ���������ڵĵ�Ƶ�����������գ�
        heightAboveGround           = 0.5;          % �������ĸ߶ȣ��ף�
        isEgoMotionEnabled          = 0;            % �Ƿ����������˶� [true, false, XX��Ĭ�ϣ�]

        % ���ߺʹ���
        peakTransmitPower           = 0.01;         % ��ֵ���͹��ʣ�10dBm��
        transmitAntennaGain         = 17;           % ���书������
        receiveAntennaGain          = 15;           % ���ܹ�������
        receiverNoiseFigure         = 10;           % ��������ϵ��
        numberOfTransmitAntennas    = 2;            % �������߸���
        numberOfReceiverAntennas    = 4;            % �������߸���
        numberOfvirtueRxannate      = 8;
        txPos                       = [];           % ��������λ��
        rxPos                       = [];           % ��������λ��
        hp_cutoff_Hz                = [];
        lp_cutoff_Hz                = [];
        MIMOTRmode                  = 'TDM-MIMO'    % MIMO�շ�ģʽ
        NumOfChirpInOneFrame        = 64;
        activateTimeInOneFrame      = 0;
        TimeOfFrame                 = 40e-3;
        NumOfFrame                  = 40;
        adcStartTime                = 2e-6;
        lambda                      = 5e-4;
        isprintNoiseCharacteristics = false;        % ��ӡSNR������ˮƽ

        % 高级帧模式相关属性
        advancedFrameMode           = false;        % 高级帧模式开关，true为双子帧模式，false为传统单子帧模式

        % 第二子帧FMCW参数
        radarFrequency2             = 77.5e9;       % 第二子帧载频
        chirpTRandEndime2           = 60e-6;        % 第二子帧线性调频上升时间
        Tidle2                      = 12e-6;        % 第二子帧chirp空闲时间
        sweepBw2                    = 0.9e12;       % 第二子帧调频斜率
        Bw2                         = 54e6;         % 第二子帧调频带宽（将在初始化时计算）
        NumOfChirpInOneSubframe2    = 64;           % 第二子帧中的chirp数量

        % 双子帧时间管理
        activateTimeSubframe1       = 0;            % 第一子帧激活时间
        activateTimeSubframe2       = 0;            % 第二子帧激活时间
        subframeGap                 = 1e-6;         % 子帧间隔时间
        % �������Ӳ� // ���ݲ��������������
        noiseFloor                  = -130;         % �ֱ���+/-dynamicNoise + receiverNoiseFigure + dBoffset��
        dynamicNoise                = 10;           % �ֱ���+/- NoiseFloor��
        isBackScatterStatClutter    = false;        % �Ƿ�����ģ�⾲̬�Ӳ�Ŀ��
        numStatTargets              = 60;           % �����ֲ��ľ�̬�Ӳ�Ŀ������
        dBoffset                    = 30;           % RDͼ��ͼ��ƫ����

        % �ں����г�ʼ����init_RDmap(obj)
        chirpInterval               = [];           % ���������ĵ�Ƶ�źţ�chirp��֮���ʱ����
        K                           = [];           % #�������
        L                           = [];           % #�ٶȲ���
        dR                          = [];           % ����ֱ���
        dV                          = [];           % �ٶȷֱ���
        rangeBins                   = [];           % ����RDͼ��bin
        velBins                     = [];           % �ٶ�RDͼ��bin
        Rmaxsamp                    = [];           % �����ģ�����루��fs���ƣ�

        % �ں����г�ʼ����generateChirpSequence(obj)
        Propagation_fs              = [];           % ģ���źŵĲ�����
        chirps                      = [];           % phased.FMCWwaveform��phased.LinearFMWaveform��Ķ���

        % �ں����г�ʼ����generateAntPattern(obj)
        VantPattern                 = [];           % ��ֱ����
        HantPattern                 = [];           % ˮƽ����

        % �ں����г�ʼ����setupMeasurement(obj)
        MSchan                      = [];
        MSradarplt                  = [];
        MStrx                       = [];
        MSRXarray                   = [];
        MSrcvx                      = [];
        systemNoiseFigure_dB        = [];
        adcInputImpedance_ohms      = [];
    end

    methods
        %% ����һ��chirp���źŷ�����Ϣ
        function amp_vect = calculate_amp_vect(obj, TX_pos, RX_pos, pointcloud)
            % Input:
            %   TX_pos: transmitter position (1x3 vector) M����������
            %   RX_pos: receiver position (Nx3 vector)    N����������
            %   cart_x: x-coordinates of points (1xN_points vector)
            %   cart_y: y-coordinates of points (1xN_points vector)
            %   cart_z: z-coordinates of points (1xN_points vector)
            %   c: speed of light
            %   fc: carrier frequency
            %   N_points: number of points to consider
            % Output:
            %   amp_vect: received signal in the antenna array
            %                   (length(t_ax) x N_points matrix)

            % ���������һ��chirp�������ǲ����ģ�����Ǻ����ģ���Ϊһ��chripʱ��̣ܶ�20us��
            N_points = length(pointcloud(:,1));
            cart_x = pointcloud(:,1);
            cart_y = pointcloud(:,2);
            cart_z = pointcloud(:,3);
            amp_vect = zeros(obj.numberOfReceiverAntennas, obj.numadcInPerChirp, N_points);

            for target = 1:N_points
                try
                    % Calculate the distance from each of the transmitter to point target
                    d_T2P = sqrt((TX_pos(1)-cart_x(target)).^2+ (TX_pos(2)-cart_y(target)).^2 +...
                        (TX_pos(3)-cart_z(target)).^2); % 1 x N_tx
                    % distance between the Rx antenna and the point reflector
                    d_P2R = sqrt((RX_pos(:,1)-cart_x(target)).^2+(RX_pos(:,2)-cart_y(target)).^2 + ...
                        (RX_pos(:,3)-cart_z(target)).^2);
%                     fprintf('[d_T2P:%6f, d_P2R:%6f]\n', d_T2P(1), d_P2R(1));
                    tau = (d_T2P+d_P2R)./obj.propagationSpeed;
                    f_delay = obj.sweepBw .*tau ./obj.adcSamplingFrequency;
                    %path_loss = repmat(1/d_T2P/d_P2R,[length(t_ax),1,1]);
%                     z1 = sinc(0.05.*cart_x(target));
                    z1 = 0.7894*sinc(0.016.*atan2d(cart_x(target),cart_z(target)))+0.18;
                    %12��23���޸ĵ����߷���ͼ����
                    z2 = 0.7894*sinc(0.03783.*atan2d(cart_y(target),cart_z(target)))+0.18;
%                     z2 = sinc(10.*cart_y(target));
                    z = abs(z1'.*z2); %����Ǵ��������߷���ͼ
%                     path_loss = (0.01./(d_T2P^2.*d_P2R.^2)).*z;
                    path_loss = (1./(d_T2P+d_P2R)).*z;% �����ǲ���ƽ��

%                     path_loss = 1;
%                     path_loss = (1./sqrt(d_T2P)./sqrt(d_P2R)).*z;
%                     path_loss = (1./d_T2P./d_P2R).*z;
                    pt_signal = (path_loss.*exp(-1j * 2 * pi * obj.radarFrequency * tau));% TODO������Ӧ��Ҫ�и��ţ����ǲ�Ӱ��
                    pt_signal = repmat(pt_signal', obj.numadcInPerChirp, 1)';
                    pt_signal = pt_signal.*exp(-1j * 2 * pi * f_delay * (0:obj.numadcInPerChirp-1));
                    amp_vect(:,:,target) = pt_signal;
                catch
                    % �쳣��������
                    disp('An error occurred. Division by zero is not allowed.');
                end
            end
        end

        function y_rx = generate_received_signal(obj, N_beacons, N_chirp, N_symb, ...
                ts, ffreq_vect, amp_vect, doppler_vect, beacon_gains)
            % Initialize the received signal
            y_rx = zeros(obj.numberOfReceiverAntennas, obj.numadcInPerChirp);

            % Get the number of points
            N_points = length(amp_vect(1,1,:));

            for target = 1:N_points
                % Calculate the frequency delay for this target
                f_delay = ts*ffreq_vect(target);

                % Calculate the nominal chirp response for this target
                y_nom_chirp = amp_vect(:,target)'.*exp(1i*f_delay*(0:N_symb-1));

                % Calculate the phase change over chirps due to Doppler (in one subframe)
                dop_phase = exp(-1i*doppler_vect(target)*(0:N_chirp-1)');

                % Calculate the nominal subframe response for this target
                y_nom = dop_phase*y_nom_chirp;

                % Calculate the phase change over subframes due to Doppler (in one frame)
                beac_dop_phase = exp(-1i*doppler_vect(target)*N_chirp*(0:N_beacons-1));

                for beacon = 1:N_beacons
                    % Apply beacon and Doppler modulation on subframes
                    y_tot_target = beac_dop_phase(beacon)*beacon_gains(target,beacon)...
                        *reshape(y_nom,1,N_chirp,N_symb);
                    % Add this target's signal to the overall received signal
                    y_rx(beacon,:,:) = y_rx(beacon,:,:) + y_tot_target;
                end
            end
        end

        %% ��ʼ�����������ͼ�ķֱ���
        function obj = init_RDmap(obj)
            %   K: �����bin����
            %   L: �ٶȷ�bin����
            %   dR: ����ֱ���
            %   dV: �ٶȷֱ���
            %   rangeBins: RDͼ��y��
            %   velBins: RDͼ��x��
            global propagationSpeed; % ȫ�ֱ���������
            obj.propagationSpeed = propagationSpeed; % ��c0����Ϊȫ�ֹ���

            if strcmp(obj.chirpShape, 'SAWgap') || strcmp(obj.chirpShape, 'TRI')
                obj.chirpInterval = 2 * obj.chirpTime; % ���һ����Ƶ+�ָ�ʱ��ļ��
            else
                obj.chirpInterval = obj.chirpTime; % ���һ����Ƶ�ļ��
            end

            obj.K = obj.chirpTime * obj.adcSamplingFrequency; % ÿ�����յ�Ƶʱ����320�����������������룩
            obj.L = obj.chirpsPerCycle; % һ�������ڹ۲⵽�ĵ�Ƶ����
            obj.dR = obj.propagationSpeed / (2 * obj.sweepBw); % ����ֱ���
            obj.dV = 1 / (obj.L * obj.chirpInterval) * obj.propagationSpeed / (2 * obj.radarFrequency); % �ٶȷֱ���
            obj.rangeBins = (0:obj.K/2-1) * obj.dR; % RDͼ��y��
            obj.velBins = (-obj.L/2:obj.L/2-1) * obj.dV; % RDͼ��x��
            % RmaxChirp = (chirpInterval-chirpTime) * c0 / 2; % ����ʱ�������Ƶ������ģ������
            obj.Rmaxsamp = obj.adcSamplingFrequency/4 * obj.propagationSpeed * obj.chirpTime / obj.sweepBw; % ����Ƶ�����Ƶ�������
            if obj.isEgoMotionEnabled == false
                obj.isEgoMotionEnabled = 0; % ��ֹ�״�
            elseif obj.isEgoMotionEnabled == true
                obj.isEgoMotionEnabled = obj.velBins(end) * rand(); % Я��������������˶�
            end
        end


        %% ���ɵ�Ƶ����
        function obj = generateChirpSequence(obj)
            % ��������Ӷ������� obj.chirps�����а���һ����Ƶ���У�chirpInterval����������
            % ���ô��������� obj.Propagation_fs = obj.sweepBw ���в����������ڽϵ� fs �³���Ƿ��������

            showplot = false; % �Ƿ����ͼ��
            obj.Propagation_fs = obj.sweepBw; % ģ�⴫���źŵĲ�����


            %TRIANGLE
            if strcmp(obj.chirpShape,'TRI')
                obj.chirps = phased.FMCWWaveform('SampleRate',obj.Propagation_fs,'SweepTime',obj.chirpTime, ...
                    'SweepBandwidth',obj.sweepBw,...
                    'SweepDirection', 'Triangle',...
                    'SweepInterval', 'Positive', 'NumSweeps', 1);

            %SAWTOOTH
            elseif strcmp(obj.chirpShape,'SAW1') %1 chirp
                obj.chirps = phased.FMCWWaveform('SampleRate',obj.Propagation_fs,'SweepTime',obj.chirpTime, ...
                    'SweepBandwidth',obj.sweepBw,...
                    'SweepDirection', 'Up',...  %SweepDirection Triangle
                    'SweepInterval', 'Positive', 'NumSweeps', 1);


            %SAWTOOTH with gap
            elseif strcmp(obj.chirpShape,'SAWgap')
                    obj.chirps = phased.LinearFMWaveform('SampleRate',obj.Propagation_fs,...
                        'SweepBandwidth',obj.sweepBw, 'PulseWidth',obj.chirpTime,...
                        'DurationSpecification','Pulse width', 'PRF', 1/obj.chirpInterval, ...
                        'SweepInterval','Positive');

            else
                fprintf('\n !!Did not recognize chirp sequence!!\n\n')
            end

            if showplot
                figure;
                plot(obj.chirps);
                figure;
                fsig = step(obj.chirps); %Samples of FMCW waveform
                windowlength = 32;
                noverlap = 16;
                nfft = 32;
                spectrogram(fsig,windowlength,noverlap,nfft,obj.chirps.SampleRate,'yaxis') %
                title('Chirp sequence')

                error('Show chirp plot and stop...')
                % Downsample chirp

            end

        end

        %% ��������ģʽ
        function obj = generateAntPattern(obj)
            az = -180:10:180;
            Vpattern = [-17, -13, -15, NaN, -17, -11, -8.5, -7.5, -5, -3.5, 0, 5, 7, 10, 11, 12, 13.5, 14, 15, 14, 13.5, 11, 12, 10, 7, 5, 0, -3.5, -5, -7.5, -8.5, -11, -17, NaN, -15, -13, -17];
            Hpattern = [-17, NaN, NaN, NaN, NaN, NaN, NaN, -20, -17, -14.5, -12, -11, -15, -10, NaN, -10, -4, 7, 15, 7, -4, -10, NaN, -10, -15, -11, -12, -14.5, -17, -20, NaN, NaN, NaN, NaN, NaN, NaN, -17];
            obj.VantPattern = repmat(Vpattern, [181, 1]);
            obj.HantPattern = repmat(Hpattern, [181, 1]);
        end

        %% ���ò���
        function obj = setupMeasurement(obj)
            % �ŵ�ģ��
            obj.MSchan = phased.FreeSpace('PropagationSpeed', obj.propagationSpeed, 'OperatingFrequency', obj.radarFrequency, ...
                'TwoWayPropagation', true, 'SampleRate', obj.Propagation_fs);
            % �״�λ�ú��˶�
            obj.MSradarplt = phased.Platform('InitialPosition', [0; 0; obj.heightAboveGround], ...
                'OrientationAxesOutputPort', true, 'InitialVelocity', [obj.isEgoMotionEnabled; 0; 0], 'Acceleration', [0; 0; 0], ...
                'MotionModel', 'Acceleration', 'AccelerationSource', 'Input port');
            % �״﷢���
            obj.MStrx = phased.Transmitter('PeakPower', obj.peakTransmitPower, 'Gain', obj.transmitAntennaGain);
            % �״���ջ�
            antenna = phased.CustomAntennaElement('AzimuthAngles', -180:10:180, 'SpecifyPolarizationPattern', true, 'HorizontalMagnitudePattern', obj.HantPattern, 'VerticalMagnitudePattern', obj.VantPattern);
            obj.MSRXarray = phased.ULA('Element', antenna, 'NumElements', obj.numberOfReceiverAntennas, 'ElementSpacing', obj.propagationSpeed / obj.radarFrequency, 'ArrayAxis', 'y');
            obj.MSrcvx = phased.ReceiverPreamp('Gain', obj.receiveAntennaGain, 'NoiseFigure', obj.receiverNoiseFigure);
        end

        %% ���ź����Ӿ��в�ͬ��׼��ĸ�˹����
        function s_beatnoisy = addGaussNoise(obj, s_beat)
            % ���룺s_beat����СΪKxL��
            % obj.SNRȷ���˸��������ķ���
            % obj.printNoiseCharacteristics = true; %���ԣ���ӡSNR������ˮƽ

            % ��������������ƥ������е�FFT���
            FFToffset = 10; %�ֱ�

            % Ӧ��ƫ�Ʋ����������������������̬
            dynOffset = ((rand()-0.5)*2)*obj.dynamicNoise;
            FFTnoiseFloor = obj.NoiseFloor + dynOffset + FFToffset; %

            % ���������������������
            Pn = 10^(FFTnoiseFloor/10);

            % ��Beat�źŹ��ʽ��бȽ�
            if strcmp(obj.chirpShape,'SAWgap')
                Ps = 1/(size(s_beat,1)/2)*sum(abs(s_beat(1:size(s_beat,1)/2,end,1).^2));
            else
                Ps = 1/(size(s_beat,1))*sum(abs(s_beat(:,end,1).^2));
            end
            if Pn>Ps*10 && Ps>0 && obj.printNoiseCharacteristics
                fprintf('ע�⣺�������� %.2f dB ������RX Beat�źŹ��� %.2f dB��\n�뿼�����ø��͵��������档\n', FFTnoiseFloor, 10*log10(Ps))
            end

            % ��ʼ������ͳ��
            sig = sqrt(Pn);
            mean = 0;
            sz = size(s_beat);
            noise = sig * randn(sz) + mean;

            noiseR = noise;
            %noiseR = sig * randn(sz) + mean;
            for a = 1:size(s_beat,3)
                FN = fftshift(fft(noiseR(:,:,a),[],1),1);
                FNR = zeros(size(FN));
                for c = 1:size(FNR,2)
                    % �źŹ��ʲR��Ϊ-10dB = 10^-1
                    FNR(:,c) = FN(:,c) .* [fliplr(10.^(-1*obj.rangeBins/obj.rangeBins(end))), 10.^(-1*obj.rangeBins/obj.rangeBins(end))]' ;
                end
                noiseR(:,:,a) = ifft(ifftshift(FNR,1),[],1);
            end
            RnoiseFaktor = 0.5+rand()*2;

            % ���ź������������������ص��Ӳ�
            if obj.printNoiseCharacteristics
                SNR = Ps/Pn;
                fprintf('���ɴ��о����������� %.2f �ĸ�˹�������档SNRΪ %.5f��%.2f �ֱ�����\n', RnoiseFaktor, SNR, 10*log10(SNR));
            end
            s_beatnoisy = s_beat + (noise + noiseR * RnoiseFaktor);
        end

        %% ���Ӿ�̬�Ӳ�
        function sbC = addStaticClutter(obj, s_beat)
            % ���ܸ��죬�ȷ�ɢ��Ŀ��
            % Pclutter_min = obj.NoiseFloor+10; %�ֱ�

            %             % ��������������ƥ��FFT���
            %             FFToffset = -60; %�ֱ�

            % ��ʼ����������ľ�̬Ŀ��
            statTarg = ceil(raylrnd(1) * obj.numStatTargets); %������������ľ�̬Ŀ��
            % statTarg��[1, 60]��Χ�ڣ���ʾ��160������bin�ϵ�Ŀ������

            % �������
            Ridx = floor(rand(statTarg,1) * length(obj.rangeBins)) + 1;
            Ridx(Ridx > length(obj.rangeBins)) = ceil(rand() * length(obj.rangeBins));
            ranges = obj.rangeBins(Ridx);

            % ������������� [NoiseFloor, NoiseFloor+20dB]
            AmpMargin = 15; %dB ����ķ���
            rAmp = obj.NoiseFloor - AmpMargin * raylrnd(ones(1, statTarg)) + 20;
            rangeAtt = 2 * AmpMargin * (ranges / 1.5 / obj.rangeBins(end)); % �����˥�����Ӳ����� ~R^2
            rAmp = sqrt(10 .^ ((rAmp - rangeAtt) / 10)); %Amp = sqrt(Pn)��dB -> scalar

            % �����Ӳ��ź�
            clutter = zeros(obj.K, obj.L, size(s_beat, 3));

            rangeIdxMat = transpose(meshgrid(0:obj.K-1, 1:obj.L)); % �����а�����0:K��������ʱ�䣩��L��Chirps������
            dopplerIdxMat = meshgrid(0:obj.L-1, 1:obj.K); % �����а���0:L��K����

            for target = 1:statTarg
                azimut = rand() * 180 - 90; % Ϊ��̬�Ӳ�������������
                for R = -obj.dR:obj.dR:+obj.dR
                    %for v = -obj.dV:obj.dV:obj.dV %��ѡ������һЩN
                    for v = 0
                        fR = - obj.sweepBw / obj.chirpTime * 2 / obj.c0 * (ranges(target) + R);
                        fd = obj.f0 * 2 / obj.c0 * (v); % obj.egoMotion + v
                        phase = obj.f0 * 2 / obj.c0 * (ranges(target) + R);
                        % ����Ƶ��ƫ�ƣ����롢�����պ���λƫ�ƣ����ź�
                        rangeMat = exp(1j * 2 * pi * fR / obj.fs * rangeIdxMat); % t = ��K������ֵ��ʱ�䣩������f_R��ÿ��Chirp��L�У�����
                        dopplerMat = exp(1j * 2 * pi * fd * obj.chirpInterval * dopplerIdxMat); % t = ��L Chirps������f_D��ÿ���������У�����
                        phaseMat = exp(1j * 2 * pi * phase); %���ھ������������λ��

                        if R == 0 && v == 0
                            ampScale = 1; %���Ĵ�������
                        else
                            ampScale = (rand() * 0.75); %��������ϵ�
                        end

                        % �����������е���λƫ��
                        % clutter = clutter + rAmp(target) * ampScale * (rangeMat .* dopplerMat .* phaseMat);
                        dx = (obj.c0 / obj.f0) / 2 * sind(azimut);
                        for ant = 1:size(s_beat, 3)
                            phaseShiftArray = exp(1j * 2 * pi * obj.f0 * (ant-1) * dx / obj.c0);
                            clutter(:, :, ant) = clutter(:, :, ant) + rAmp(target) * ampScale * (rangeMat .* dopplerMat .* phaseMat) * phaseShiftArray;
                        end
                    end
                end
            end

            sbC = s_beat + real(clutter);
        end

    %% �Ӹ�����beat�ź�s_beat����Range Doppler Map
        function RDmap = RDmap(obj, s_beat)
            % ���룺s_beat������KxLxA��
            sz = size(s_beat);
            RDmap = zeros(sz(1)/2,sz(2), sz(3));

        %     for ant = 1:obj.RXant
        %         %ѡ��һ��RX����
        %         sb = s_beat(:,:,ant);
        %
        %         if size(sb,1) ~= obj.K
        %             error('\nBeat�źŵĴ�С����ȷ��[%d,%d]��������K��L�ǣ�[%d,%d]��\nԤ�ڵ�����K�ĳ�����beat�źŵ��в�ƥ��...\n\n', size(sb,1), size(sb,2), obj.K, obj.L)
        %         elseif size(sb,2) ~= obj.L
        %             error('\nBeat�źŵĴ�С����ȷ��[%d,%d]��������K��L�ǣ�[%d,%d]��\nԤ�ڵ���������L�ĳ�����beat�źŵ��в�ƥ��...\n\n', size(sb,1), size(sb,2), obj.K, obj.L)
        %         end

            % ��һ��FFT����RANGE
            winK = repmat(hann(obj.K), [1,obj.L,obj.RXant]);
            scaWinK = sum(winK(:, 1, 1));
            s_beat = cat(1,zeros(0, obj.L, obj.RXant), s_beat .* winK, zeros(0, obj.L, obj.RXant));
            SB = fft(ifftshift(s_beat,1), obj.K, 1) / scaWinK;
        %     SB = fft(s_beat,[], 1)/obj.K; %/����(s_beat(:,1))����ÿһ�н���FFT����K��ʱ������
            SB = fftshift(SB,1);

            %���ӻ�����
            %SB = SB(1:obj.K/2,:,:);
            SB = SB(obj.K/2+1:end,:,:); %����ʾ��Range


            % �ڶ���FFT����DOPPLER
            winL = repmat(hann(obj.L)', [obj.K/2, 1, obj.RXant]);
            scaWinL = sum(winL(1,:,1));
            SB = cat(2, zeros(obj.K/2, 0, obj.RXant), SB .* winL, zeros(obj.K/2, 0, obj.RXant));
            SB = fft(ifftshift(SB,2), obj.L, 2) / scaWinL;
        %     SB = fft(SB, [], 2) /obj.L; % /256  ��ÿһ�н���FFT����L������
            SB = fftshift(SB,2);

            % ������FFT���ڽǶ�
            winN = zeros(1,1,obj.RXant);
            winN(:) = hann(obj.RXant);
            winN = repmat(winN, [obj.K/2, obj.L, 1]);

            % ���160x256x16
        %     SB = cat(3, zeros(obj.K/2, obj.L, obj.RXant/2), SB .* winN, zeros(obj.K/2, obj.L, obj.RXant/2));
        %     SB = fft(ifftshift(SB,3), obj.RXant*2, 3)/sum(winN(1,1,:));
            % ���160x256x8
            SB = cat(3, zeros(obj.K/2, obj.L, 0), SB .* winN, zeros(obj.K/2, obj.L, 0));
            SB = fft(ifftshift(SB,3), obj.RXant, 3) / sum(winN(1,1,:));

            SB = fftshift(SB,3);
            SB = flip(SB, 3); %��������RX���ߣ�RXͨ����1:8��= [+90��-90]

            RDmap = 10 * log10(abs(SB).^2); %�Զ����̶ȱ�ʾ��RDͼ

            % ��ѡ��RDmapԤ����
            %�����(RD)����Ϊ0
        %     RDmap = RDmap - max(RDmap);
            %����Сֵ����Ϊ-150dB
        %     RDmap(RDmap < -200) = -200;
        end


        %% ����Range Dopplerͼ
        function plotRDmap(obj, RDmap, target, plotAntennas)
            % ���룺RDmap������RDmap()��

            if plotAntennas == 0
                % �����Ʒ�Χ������ͼ��ɾ����λ������
                % ȥ����һ����Χbin
                rangeDoppler = RDmap(6:end, :,:);
                % �ڽǶ�bin��ȡ���ֵ
                rangeDoppler = max(rangeDoppler,[], 3);

                % ��RD��ת��Ϊ�Ҷ�ͼ�����ֵΪ-20����СֵΪ-85�����ֵ��0��1֮�䡣���ֵ����Сֵ�������ݽ��е������Ա���д�Լ65 dB�Ķ�̬��Χ
                figure;
                x = obj.velBins; % +obj.egoMotion Speed with egoMotion
                y = obj.rangeBins(1:length(obj.rangeBins/2));
                imagesc(x*3.6,y,rangeDoppler+obj.dBoffset) % ����������ʵ���ݽ��бȽϣ�����60dB��ƫ��
                set(gca,'YDir','normal')
                xlabel('�ٶȣ�km/h��')
                ylabel('��Χ��m��')
                colorbar
                colormap jet
                title('��dBΪ��λ�ĺ���ɢ�书�ʵķ�Χ-������ͼ')

            else
                for ant = plotAntennas
                    RDmap_plt = RDmap(:,:,ant);
                    figure;
                    x = obj.velBins; % +obj.egoMotion Speed with egoMotion
                    y = obj.rangeBins(1:length(obj.rangeBins/2));
        %             y = [fliplr(y),y];
                    if ~isempty(target) %��ʾ��ǰĿ��λ��
                        % ��Ŀ���R��Vת��ΪRDͼ�е�����
                        [~,targetRidx] = min(abs(target(1)-obj.rangeBins));
                        [~,targetVidx] = min(abs(target(2)-obj.velBins));

                        RDmap_plt = insertMarker(RDmap_plt, [targetVidx, targetRidx]); % ���ӱ��
                        % bRD = insertShape(bRD,'circle',[150 280 35],'LineWidth',5);
                        fprintf('ģ���Ŀ����ʼ��ΧΪ%.2f�ף������ٶ�Ϊ%.2f��/�롣\n', ...
                        target(1), target(2))
                    end
                    imagesc(x*3.6,y,RDmap_plt(1:end,:,1)+obj.dBoffset) % ����������ʵ���ݽ��бȽϣ�����60dB��ƫ��
                    set(gca,'YDir','normal')
                    xlabel('�ٶȣ�km/h��')
                    ylabel('��Χ��m��')
                    colorbar
                    colormap jet
                    title(['��dBΪ��λ��RX����', num2str(ant), '�ĺ���ɢ�书������'])
                end
            end
        end


    end
end

