function a_phi_range2D = GenerateSteering(tx_num, rx_num, delta_range, k, txPos, rxPos, lamada, fft1d_num, pisearchx, is_spread, is_nearfield)
    %% 
    % 生成导向矢量
    a_phi_range2D = [];
    for j=1:fft1d_num
        a_phi = [];
        r = delta_range * j;
        azimuth_t = pisearchx;
        x = r .* cosd(90-azimuth_t);
        y = zeros(1,length(azimuth_t));
        z = r .* sind(90-azimuth_t);
        point1 = [x; y; z];
        a = zeros(1,tx_num*rx_num);
        % scatter3(point1(1,:),point1(2,:),point1(3,:));
        % hold on;
        for phi = 1 : length(azimuth_t)
            
            if (is_spread)
                angle_spread = max(1,phi-(1+round(2*(fft1d_num-j+1)/fft1d_num))):min(length(azimuth_t),phi+(1+round(2*(fft1d_num-j+1)/fft1d_num)));
%                 length(angle_spread)
                %%均匀分布
%                 g_phi = ones(1,length(angle_spread));
                %高斯分布
                g_phi = exp(-(angle_spread-phi).^2/4);
            end
            
            for tx = 1 :  tx_num
                for rx = 1 :  rx_num
                    
                    if (is_spread)
                        if (is_nearfield)
                            euclidean_distance1 = sqrt((point1(1,angle_spread)'-txPos(tx,1)).^2+(point1(2,angle_spread)'-txPos(tx,2)).^2+(point1(3,angle_spread)'-txPos(tx,3)).^2);
                            euclidean_distance2 = sqrt((point1(1,angle_spread)'-rxPos(rx,1)).^2+(point1(2,angle_spread)'-rxPos(rx,2)).^2+(point1(3,angle_spread)'-rxPos(rx,3)).^2);
                            dt = euclidean_distance1 + euclidean_distance2;
                            a((tx-1)*rx_num+rx) = sum(g_phi'.*exp(-1i* k.* dt));
                        else
                            dt = ((tx-1)*rx_num+rx).*lamada/2.*sind(azimuth_t(angle_spread)');
                            a((tx-1)*rx_num+rx) = sum(g_phi'.*exp(-1i* k.* dt));

                        end
                    else
                        if (is_nearfield)
                            euclidean_distance1 = pdist([point1(:,phi)';txPos(tx,:)], 'euclidean');
                            euclidean_distance2 = pdist([point1(:,phi)';rxPos(rx,:)], 'euclidean');
%                             euclidean_distance1 = pdist([point1(:,phi)';[0,0,0]], 'euclidean');
%                             euclidean_distance2 = pdist([point1(:,phi)';[0,0,0]], 'euclidean');
                            dt = euclidean_distance1 + euclidean_distance2;
%                             a((tx-1)*rx_num+rx) = 1/dt^2*exp(-1i* k * dt); %1/dt^2这个系数肯定有问题
                            a((tx-1)*rx_num+rx) = exp(-1i* k * dt);
%                             a((tx-1)*rx_num+rx) = 1/dt^2*exp(-1i* (k+2*pi*j*2e14/2e6/3e8) * dt);
                        else
                            dt = ((tx-1)*rx_num+rx).*lamada/2.*sind(-azimuth_t(phi));
                            a((tx-1)*rx_num+rx) = exp(-1i* k.* dt);

                        end

                    end        
                end
            end
            
            a_phi = cat(1, a_phi, a);

        end
%         scatter3(point1(1,:),point1(2,:),point1(3,:),5,abs(a_phi(:,1)-a_phi(:,2)),'filled');
%         hold on;
        a_phi_range2D = cat(3, a_phi_range2D, a_phi);

    end

end