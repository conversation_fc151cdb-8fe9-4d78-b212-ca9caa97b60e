%% 高级帧模式雷达数据处理器 - 快速启动脚本
% 一键启动雷达数据处理系统

function quick_start_radar_processor()
    clear; clc; close all;
    
    fprintf('========================================\n');
    fprintf('  高级帧模式雷达数据处理器\n');
    fprintf('  快速启动脚本\n');
    fprintf('========================================\n\n');
    
    %% 1. 环境检查
    fprintf('1. 检查运行环境...\n');
    
    % 检查MATLAB版本
    matlab_version = version('-release');
    fprintf('   MATLAB版本: %s\n', matlab_version);
    
    % 检查必需工具箱
    required_toolboxes = {
        'Signal Processing Toolbox',
        'Phased Array System Toolbox'
    };
    
    for i = 1:length(required_toolboxes)
        if license('test', strrep(required_toolboxes{i}, ' ', '_'))
            fprintf('   ✓ %s 可用\n', required_toolboxes{i});
        else
            fprintf('   ⚠ %s 不可用（可能影响部分功能）\n', required_toolboxes{i});
        end
    end
    
    %% 2. 文件检查
    fprintf('\n2. 检查必需文件...\n');
    
    required_files = {
        'advanced_frame_radar_processor.m',
        'data_reduced_precision_advanced_frame.mat'
    };
    
    missing_files = {};
    for i = 1:length(required_files)
        if exist(required_files{i}, 'file')
            fprintf('   ✓ %s\n', required_files{i});
        else
            fprintf('   ❌ %s\n', required_files{i});
            missing_files{end+1} = required_files{i};
        end
    end
    
    %% 3. 处理缺失文件
    if ~isempty(missing_files)
        fprintf('\n⚠ 发现缺失文件，正在尝试解决...\n');
        
        for i = 1:length(missing_files)
            file = missing_files{i};
            
            if strcmp(file, 'data_reduced_precision_advanced_frame.mat')
                fprintf('   数据文件缺失，需要先生成数据\n');
                
                choice = questdlg(['数据文件不存在。是否现在运行数据生成程序？\n' ...
                    '(这将运行 Copy_of_golf_of_MIMO_of_main.m)'], ...
                    '生成数据', '是', '否', '取消', '是');
                
                switch choice
                    case '是'
                        fprintf('   正在生成雷达数据...\n');
                        try
                            % 检查数据生成程序是否存在
                            if exist('Copy_of_golf_of_MIMO_of_main.m', 'file')
                                fprintf('   启动数据生成程序...\n');
                                fprintf('   注意：确保在数据生成程序中设置了 fmcw.advancedFrameMode = true\n\n');
                                
                                % 提示用户检查设置
                                fprintf('   请确认以下设置：\n');
                                fprintf('   1. 在 Copy_of_golf_of_MIMO_of_main.m 中\n');
                                fprintf('   2. 找到 fmcw.advancedFrameMode = true; 这一行\n');
                                fprintf('   3. 确保该值为 true\n\n');
                                
                                input('   按回车键继续运行数据生成程序...');
                                
                                % 运行数据生成程序
                                run('Copy_of_golf_of_MIMO_of_main.m');
                                
                                % 检查是否成功生成
                                if exist('data_reduced_precision_advanced_frame.mat', 'file')
                                    fprintf('   ✓ 数据生成成功\n');
                                else
                                    fprintf('   ❌ 数据生成失败\n');
                                    return;
                                end
                            else
                                fprintf('   ❌ 找不到 Copy_of_golf_of_MIMO_of_main.m\n');
                                return;
                            end
                        catch ME
                            fprintf('   ❌ 数据生成失败: %s\n', ME.message);
                            return;
                        end
                        
                    case '否'
                        fprintf('   请手动运行数据生成程序后再启动处理器\n');
                        return;
                        
                    case '取消'
                        fprintf('   用户取消操作\n');
                        return;
                end
                
            elseif strcmp(file, 'advanced_frame_radar_processor.m')
                fprintf('   ❌ 处理器主程序文件缺失\n');
                fprintf('   请确保 advanced_frame_radar_processor.m 在当前目录中\n');
                return;
            end
        end
    end
    
    %% 4. 数据验证
    fprintf('\n3. 验证数据文件...\n');
    try
        data = load('data_reduced_precision_advanced_frame.mat');
        
        if ~isfield(data, 'fmcw') || ~data.fmcw.advancedFrameMode
            fprintf('   ❌ 数据不是高级帧模式生成的\n');
            fprintf('   请确保在数据生成时设置 fmcw.advancedFrameMode = true\n');
            return;
        end
        
        fprintf('   ✓ 数据验证通过\n');
        fprintf('   数据维度: [%d, %d, %d, %d]\n', size(data.RawDataMatrix));
        fprintf('   第一子帧chirps: %d\n', data.fmcw.NumOfChirpInOneFrame);
        fprintf('   第二子帧chirps: %d\n', data.fmcw.NumOfChirpInOneSubframe2);
        
    catch ME
        fprintf('   ❌ 数据验证失败: %s\n', ME.message);
        return;
    end
    
    %% 5. 启动选项
    fprintf('\n4. 选择启动模式...\n');
    fprintf('   1. 直接启动处理器\n');
    fprintf('   2. 先运行测试\n');
    fprintf('   3. 查看使用说明\n');
    
    choice = input('   请选择 (1-3): ');
    
    switch choice
        case 1
            fprintf('\n正在启动雷达数据处理器...\n');
            fprintf('========================================\n\n');
            try
                advanced_frame_radar_processor();
            catch ME
                fprintf('❌ 处理器启动失败: %s\n', ME.message);
                fprintf('建议先运行测试模式检查问题\n');
            end
            
        case 2
            fprintf('\n正在运行测试...\n');
            fprintf('========================================\n\n');
            try
                if exist('test_radar_processor.m', 'file')
                    test_radar_processor();
                else
                    fprintf('测试文件不存在，直接启动处理器...\n');
                    advanced_frame_radar_processor();
                end
            catch ME
                fprintf('❌ 测试失败: %s\n', ME.message);
            end
            
        case 3
            fprintf('\n显示使用说明...\n');
            if exist('雷达数据处理器使用说明.md', 'file')
                fprintf('请查看 "雷达数据处理器使用说明.md" 文件\n');
                if ispc
                    system('notepad 雷达数据处理器使用说明.md');
                elseif ismac
                    system('open 雷达数据处理器使用说明.md');
                else
                    system('gedit 雷达数据处理器使用说明.md');
                end
            else
                show_quick_help();
            end
            
        otherwise
            fprintf('无效选择，启动默认模式...\n');
            advanced_frame_radar_processor();
    end
    
    fprintf('\n========================================\n');
    fprintf('感谢使用高级帧模式雷达数据处理器！\n');
    fprintf('========================================\n');
end

function show_quick_help()
    fprintf('\n=== 快速使用指南 ===\n');
    fprintf('1. 界面控制：\n');
    fprintf('   - 播放：自动播放所有帧\n');
    fprintf('   - 暂停：暂停播放\n');
    fprintf('   - 单步：逐帧处理\n');
    fprintf('   - 滑块：快速跳转\n\n');
    
    fprintf('2. 显示窗口：\n');
    fprintf('   - 左上：第一子帧RD图 (77 GHz)\n');
    fprintf('   - 右上：第二子帧RD图 (77.5 GHz)\n');
    fprintf('   - 左下：子帧差异图\n');
    fprintf('   - 右下：解模糊速度序列\n\n');
    
    fprintf('3. 保存功能：\n');
    fprintf('   - 点击"保存结果"按钮\n');
    fprintf('   - 自动生成时间戳文件\n');
    fprintf('   - 包含速度数据和处理报告\n\n');
    
    fprintf('按回车键继续...');
    input('');
end
