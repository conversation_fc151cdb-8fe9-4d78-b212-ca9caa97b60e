
% 清空工作空间和命令窗口，关闭所有打开的图形窗口
close all;
clearvars;
clc;

% 设置调试断点，以便在发生错误时暂停程序执行
dbstop if error;
warning off;
% ------------------------ 参数设置 ------------------------
% 载频和带宽设置
F0 = 77e9;     % 载频 (Hz)
B = 56e6;     % 带宽 (Hz)

% 信号时长和调制参数
T = 56e-6;     % 信号时长 (s)
u = B / T;     % 调制参数

% 光速和波长
c = 3E8;
lamada = c / F0;

% 波束宽度和波束宽度一半
D = lamada / 2;

% 采样参数
samplenum = 268;      % 采样点数
FS = 5000e3;    %采样速率2M(Hz)
T_s = samplenum*1/FS;
% T_s才是采集的到的事件，T仅是设置的ramp事件
t = (1/FS : 1/FS : samplenum/FS);  % 时间向量

% 模拟参数
PL = 1;                   % 快拍数
RXannate = 4;             % 接收天线数
TXannate = 2;             % 发射天线数
virtueRxannate = TXannate * RXannate;
% 距离分辨率
rsolution = c / (2 * B);
% 数字下采样
downsample  =       1;
% 最大距离范围
R_max = c*(FS/downsample)/(2*u); %单位m


% Array 
%                                      2*lambda
% RX4<-->RX3<-->RX2<-->RX1<----->TX2<------------>TX1

% 天线阵列
tx2Pos = [0, 0, 0] .* lamada;
tx1Pos = [-2, 0, 0] .* lamada;
txPos = [tx1Pos; tx2Pos];
rx_offset = [-3 * lamada, 0, 0];
rx4Pos = [0, 0, 0] .* lamada/2 + rx_offset;
rx3Pos = [-1, 0, 0] .* lamada/2 + rx_offset;
rx2Pos = [-2, 0, 0] .* lamada/2 + rx_offset;
rx1Pos = [-3, 0, 0] .* lamada/2 + rx_offset;
rxPos = [rx1Pos; rx2Pos; rx3Pos; rx4Pos];




rawData = load('data_reduced_precision.mat');
rawData = rawData.RawDataMatrix;
%%
frame_num   =       size(rawData,1);
chirp_num   =       size(rawData,2);
adcnum      =       size(rawData,3);
tx_num      =       2;
rx_num      =       4;
downsample  =       1;

adc_data = permute(rawData, [1, 2, 4, 3]); 
adc_data = downsample * adc_data(:,:,:,1:downsample:end);


PWR_threshold = 9.3;
NOISE_threshold = 2;

shape   =   size(adc_data);    
w       =   reshape(hamming(shape(4)),1,1,1,[]);%generate window
adc_data = adc_data - mean(adc_data,4);% 去除直流分量
data = adc_data.*w;
fft1d_num = 512;
radar_cube = fft(data,fft1d_num,4);
radar_mean = mean(radar_cube,2);
% radar_mean = reshape(radar_mean,96,64,8,1);
% radar_mean = sum(mean(radar_cube(1:4,:,:,:),2),1)/4;% 这个是直流去除 用前几帧没有目标的信号去除天线泄露的信号
% radar_cube = radar_cube-radar_mean;
% radar_cube(abs(radar_cube)<NOISE_threshold)=0;


% # 计算一帧的能量
frame_PWR = log(sum(abs(radar_cube),[2 3 4]));

scaling_factor = ones(1,frame_num);

fft2d_in = permute(radar_cube,[1, 4, 3, 2]);
shape   =   size(fft2d_in); 
w       =   reshape(hamming(shape(4)),1,1,1,[]);%generate window
fft2d_in = fft2d_in.*w;
fft2d_out = fft(fft2d_in,64,4);
fft2d_out = permute(fft2d_out,[1, 4, 3, 2]);

fft2d_out = fftshift(fft2d_out,2);

fft2d_out = fftshift(fft2d_out,4);



% % fft1d_in = permute(radar_cube(:,:,[1,2,3,4,5,6,7,8],:),[1, 2, 4, 3]);
% % fft1d_in = permute(radar_cube(:,:,[8,7,6,5,4,3,2,1],:),[1, 2, 4, 3]);
% % shape           =   size(fft1d_in);          
% % w               =   reshape(hamming(shape(4)),1,1,1,[]);%generate window
% % fft1d_in        =   fft1d_in.*w;
% % range_azimuth   =   fft(fft1d_in, 64, 4);
% % range_azimuth   =   fftshift(squeeze(sum(range_azimuth(:,1:3,:,:),2)),3);
% % range_azimuth   =   flip(range_azimuth,3);
% % range_azimuth   =   flip(range_azimuth,2);
% % % # 抑制脉冲能量 归一化能量
% % % rai_abs = abs(range_azimuth(i,1,:,:)));
% % range_azimuth = abs(range_azimuth)./MAX_power;

delta_range = R_max/fft1d_num; % 径向距离分辨单元

% Creat grid table
rng_ = linspace(delta_range*1,R_max,fft1d_num);

pisearchx = linspace(-90,90,64);
pisearchy = linspace(-90,90,90);

rng_grid = rng_'*cosd(pisearchx);
agl_grid = rng_'*sind(pisearchx);
k = 2*pi*F0/c;
% 
% a_phi_range2D = GenerateSteering(tx_num, rx_num, delta_range, k, txPos, rxPos, lamada, fft1d_num, pisearchx, 0, 0);
% 
% 
% range_azimuth = zeros(frame_num,fft1d_num,length(pisearchx));
% relatematrix_2 = zeros(8,8,fft1d_num);
% 
% %%   
% for i=1:frame_num
%     fft1d_out_onefame = squeeze(radar_cube(i,:,:,:));
% 
%     for j=1:fft1d_num
%         fft_data = squeeze(fft1d_out_onefame(:,:,fft1d_num-j+1));
%         relatematrix = 0.3*(fft_data') *fft_data +  0.2*relatematrix_2(:,:,j);
%         relatematrix_2(:,:,j) = relatematrix;
%         % relatematrix = (fft_data') *fft_data;
%         % 下面这个代码非常重要 进行平滑
%         % relatematrix = 0.5 * (relatematrix + conj(flip(relatematrix)));
% 
%         for phi = 1 : length(pisearchx)
%             a = a_phi_range2D(phi,:,fft1d_num);
%             MVDR(j,phi) = abs(a * relatematrix * a');
%         end     
%     end
% 
%     range_azimuth(i,:,:) = MVDR;
% 
% end




kq = ones(64,64);
queue_center =zeros(5,2);
queue_center1 =zeros(0,2);


global P
global Q
global R

global dk
global xc

dk = 0;
xc = 0;
P = eye(2);
Q = [1e-2,0;0,1e-2];
R = [0.05,0;0,0.005];
queue_track = zeros(1,2);


set(groot, 'defaultAxesFontName', 'Times New Roman');
set(groot, 'defaultTextFontName', 'Times New Roman');

fig = figure(1);
% 设置整个窗口背景颜色为纯白
set(fig, 'Color', 'w');
pos = fig.Position;


% 设置窗口大小为 800x600 像素
pos(1) = 200;
pos(2) = 200;
pos(3) = 1320;
pos(4) = 320;

% 设置新的窗口位置和大小
fig.Position = pos;

% 创建子图并设置固定大小值

ax1 = axes('Parent', fig, 'Units', 'pixels', 'Position', [80, 180,320, 100]);
ax2 = axes('Parent', fig, 'Units', 'pixels', 'Position', [80, 40, 320, 100]);
ax3 = axes('Parent', fig, 'Units', 'pixels', 'Position', [440,40, 240, 240]);
ax4 = axes('Parent', fig, 'Units', 'pixels', 'Position', [720,40, 240, 240]);
ax5 = axes('Parent', fig, 'Units', 'pixels', 'Position', [1000,40, 240, 240]);

%% 绘制x的局部高斯过程回归
hold(ax1, 'on') % 将绘图保持在同一个图中
title(ax1, 'Angle-Time', 'FontSize', 14)

%画真值
h10 = scatter(ax1, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [0.5, 0.2, 0.6], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [0.5, 0.2, 0.6], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.2, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

% %画局部预测的值
% h11 = scatter(ax1, NaN, NaN, ...                % 数据点的x和y坐标
%     25, ...                      % 数据点大小
%     [109/255, 173/255, 209/255], ...          % 数据点填充颜色（自定义颜色）
%     'filled', ...                 % 填充数据点
%     'MarkerEdgeColor', [109/255, 173/255, 209/255], ...   % 数据点边缘颜色（绿色）
%     'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
%     'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

%画滤波的值
h12 = scatter(ax1, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [183/255, 034/255, 048/255], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [183/255, 034/255, 048/255], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

h13 = fill(ax1, [NaN,NaN,NaN], [NaN,NaN,NaN] ,[183/255, 034/255, 048/255],'FaceAlpha', 0.5, 'EdgeAlpha', 0.5, 'EdgeColor', [183/255, 034/255, 048/255]);%画不确定度区间
xlim(ax1, [0 296])
ylim(ax1, [0 64])
% 横纵坐标显示
x1_label = xlabel(ax1, 't', 'FontSize', 12);
ylabel(ax1, 'angle', 'FontSize', 12);

% % 获取当前位置
% pos = x1_label.Position;
% % 向上移动X轴标签（增加第2个坐标值）
% pos(2) = pos(2) + 10; % 在这里，0.1是您要向上移动的距离，可以根据需要调整
% % 更新X轴标签的位置
% x1_label.Position = pos;

% 关闭x轴和y轴的刻度值
xticks(ax1, []);
yticks(ax1, []);

% 关闭x轴和y轴的刻度线
ax1.XAxis.TickLength = [0 0];
ax1.YAxis.TickLength = [0 0];

% 保留封闭的box
box(ax1, 'on');

% 添加图标
legend(ax1, 'Raw','Filtered')

hold(ax1, 'off');

%% 绘制y的局部高斯过程回归
hold(ax2, 'on') % 将绘图保持在同一个图中
title(ax2, 'Range-Time', 'FontSize', 14)
%画真值
h20 = scatter(ax2, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [0.5, 0.2, 0.6], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [0.5, 0.2, 0.6], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.2, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

% %画局部预测的值
% h21 = scatter(ax2, NaN, NaN, ...                % 数据点的x和y坐标
%     25, ...                      % 数据点大小
%     [109/255, 173/255, 209/255], ...          % 数据点填充颜色（自定义颜色）
%     'filled', ...                 % 填充数据点
%     'MarkerEdgeColor', [109/255, 173/255, 209/255], ...   % 数据点边缘颜色（绿色）
%     'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
%     'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

%画滤波后的值
h22 = scatter(ax2, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [183/255, 034/255, 048/255], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [183/255, 034/255, 048/255], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

h23 = fill(ax2, [NaN,NaN,NaN], [NaN,NaN,NaN] ,[183/255, 034/255, 048/255],'FaceAlpha', 0.5, 'EdgeAlpha', 0.5, 'EdgeColor', [183/255, 034/255, 048/255]);%画不确定度区间

xlim(ax2, [0 296])
ylim(ax2, [0 64])

% 横纵坐标显示
x_label = xlabel(ax2, 't', 'FontSize', 12);
ylabel(ax2, 'range', 'FontSize', 12);

% % 获取当前位置
% pos = x_label.Position;
% % 向上移动X轴标签（增加第2个坐标值）
% pos(2) = pos(2) + 0.2; % 在这里，0.1是您要向上移动的距离，可以根据需要调整
% % 更新X轴标签的位置
% x_label.Position = pos;

% 关闭x轴和y轴的刻度值
xticks(ax2, []);
yticks(ax2, []);

% 关闭x轴和y轴的刻度线
ax2.XAxis.TickLength = [0 0];
ax2.YAxis.TickLength = [0 0];

% 保留封闭的box
box(ax2, 'on');

% 添加图标
legend(ax2, 'Raw','Filtered')
hold(ax2, 'off');

%% 绘制xy联合局部高斯过程回归
hold(ax3, 'on') % 将绘图保持在同一个图中
title(ax3, 'Angle-Range', 'FontSize', 16)

%画真值
h30 = scatter(ax3, NaN, NaN, ...                % 数据点的x和y坐标
    50, ...                      % 数据点大小
    [0.2, 0.2, 0.2], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [0.2, 0.2, 0.2], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.2, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

%画滤波后的
h31 = scatter(ax3, NaN, NaN, ...                % 数据点的x和y坐标
    50, ...                      % 数据点大小
     [183/255, 034/255, 048/255], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor',  [183/255, 034/255, 048/255], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度
h32 = plot(ax3, NaN, NaN, 'LineWidth', 2,  'Color', [120/255, 120/255, 120/255, 0.4]); 
axis(ax3, 'equal');
xlim(ax3, [1 64])
ylim(ax3, [1 64])

% 横纵坐标显示
xlabel(ax3, 'angle', 'FontSize', 14);
ylabel(ax3, 'range', 'FontSize', 14);

% 关闭x轴和y轴的刻度值
xticks(ax3, []);
yticks(ax3, []);

% 关闭x轴和y轴的刻度线
ax3.XAxis.TickLength = [0 0];
ax3.YAxis.TickLength = [0 0];

% 保留封闭的box
box(ax3, 'on');
axis(ax3, 'equal');

% 添加图标
% legend(ax4, [h31,h32, h33], { 'Resampling Point','Predicted Trajectory', 'Uncertain Region'})
hold(ax3, 'off');

%% 绘制笛卡尔坐标系
hold(ax4, 'on') % 将绘图保持在同一个图中
title(ax4, 'Reconstructed Trajectory', 'FontSize', 16)
[X1,Y1] = meshgrid(1:1:64,1:1:64);
mesh_P = [X1(:),Y1(:)];
Z = zeros(size(X1));
h40 = plot(ax4, NaN, NaN, 'LineWidth', 10,  'Color', [183/255, 034/255, 048/255, 0.4]);        % 设置数据点边缘颜色透明度
h41 = plot(ax4, NaN, NaN, 'LineWidth', 2,  'Color', [123/255, 066/255, 048/255, 0.4]); 
% 横纵坐标显示
xlabel(ax4, 'x', 'FontSize', 14);
ylabel(ax4, 'y', 'FontSize', 14);

% 关闭x轴和y轴的刻度值
xticks(ax4, []);
yticks(ax4, []);


% 关闭x轴和y轴的刻度线
ax4.XAxis.TickLength = [0 0];
ax4.YAxis.TickLength = [0 0];

axis(ax4, 'equal');
xlim(ax4, [1 64])
ylim(ax4, [1 64])


% 保留封闭的box
box(ax4, 'on');
hold(ax4, 'off');

%% 绘制AR特征图
hold(ax5, 'on') % 将绘图保持在同一个图中
title(ax5, 'Range-Angle Feature', 'FontSize', 16)

h_img = image(ax5,NaN);
h_scatter = scatter(ax5,NaN, NaN,'MarkerEdgeColor', [0.5 0 0.5],'LineWidth',0.1,'MarkerEdgeAlpha',0.3);
h_center = scatter(ax5,NaN, NaN, 'filled', 'MarkerFaceColor', 'w', 'MarkerEdgeColor', 'w');
axis(ax5, 'equal');


% 横纵坐标显示
xlabel(ax5, 'angle', 'FontSize', 14);
ylabel(ax5, 'range', 'FontSize', 14);

% 关闭x轴和y轴的刻度值
xticks(ax5, []);
yticks(ax5, []);

% 关闭x轴和y轴的刻度线
ax5.XAxis.TickLength = [0 0];
ax5.YAxis.TickLength = [0 0];
% 保留封闭的box
box(ax5, 'on');
hold off

btn = uicontrol('Style', 'pushbutton', 'String', 'start', ...
                'Position', [80, 10, 60, 20], 'Callback', @break_blocking_callback);            
 
% 阻塞状态
while true
    % 当用户按下按钮时，回调函数会改变按钮的UserData属性
    if isequal(get(btn, 'UserData'), true)
        break;
    end
    pause(0.5); % 避免过度占用计算资源
end
h = [];    
for i=1:frame_num
    rai_abs = squeeze(sum(squeeze(fft2d_out(i,:,:,:)),2));
    % rai_abs = squeeze(range_azimuth(i,:,:));
    rai_abs = abs(rai_abs);
    rai_abs_flipped = flipud(rai_abs');
    set(h_img, 'CData', rai_abs_flipped/max(max(rai_abs_flipped))*255);
    
    % # 聚类轨迹点
    if 0
        
        [range, angle] = find(rai_abs>=(0.55*max(max(rai_abs))));%找到大于0.55*Max的点
        data = [range, angle];
        [cluster_max_center, cluster_max] = grow_clusters(data);

        %将第一个点当作输入
        tracklen = size(queue_track);
        tracklen = tracklen(1);
        if tracklen==1
            queue_track = cluster_max_center;
        end

        filter_point = kalmanandsmooth_filter(cluster_max_center, queue_track(end,:));
        queue_track = [queue_track;filter_point'];

        %更新图1
        set(h10, 'XData', [get(h10,'XData') i+1], 'YData', [get(h10,'YData') cluster_max_center(2)]);
        set(h12, 'XData', [get(h12,'XData') i+1], 'YData', [get(h12,'YData') queue_track(end,2)]);

        %更新图2
        set(h20, 'XData', [get(h20,'XData') i+1], 'YData', [get(h20,'YData') cluster_max_center(1)]);
        set(h22, 'XData', [get(h22,'XData') i+1], 'YData', [get(h22,'YData') queue_track(end,1)]);


        %更新图5
%         set(h_scatter, 'XData',cluster_max(:,2),'YData',cluster_max(:,1));
%         set(h_center,  'XData',cluster_max_center(:,2),'YData',cluster_max_center(:,1));
        %更新图3
        set(h30, 'XData', [get(h30,'XData') cluster_max_center(2)], 'YData', [get(h30,'YData') cluster_max_center(1)]);
        set(h31, 'XData',cluster_max_center(2), 'YData', cluster_max_center(1));
        set(h32, 'XData', [get(h32,'XData') cluster_max_center(2)], 'YData', [get(h32,'YData') cluster_max_center(1)]);
%         set(h31, 'XData', queue_track(:,2), 'YData', queue_track(:,1));
        %更新图4
        queue_track1 = [(queue_track(:, 1)+10).*sin(asin(2*queue_track(:, 2)/64-1)),(queue_track(:, 1)+10).*cos(asin(2*queue_track(:, 2)/64-1))];
%         set(h40, 'XData', queue_track1(:,1)+32, 'YData', queue_track1(:,2));
        x = queue_track1(:,1);
        y = queue_track1(:,2);

        set(h41, 'XData', x+32, 'YData', y);
    else
        set(h_scatter, 'XData',NaN,'YData',NaN);
        set(h_center, 'XData',NaN,'YData',NaN);

    end
    
        % 创建一个热图颜色映射
    cmap = colormap(parula);

    pause(0.002)
end



function y = kalmanandsmooth_filter(meansure_point, predict_point)
    global P
    global Q
    global R

    P =  P  + Q;         % 卡尔曼公式2
    K = P * inv(P + R); % 卡尔曼公式3
    kf_point = predict_point' + K * (meansure_point'-predict_point');    % 卡尔曼公式4
    P = (eye(2)-K) * P;                       % 卡尔曼公式5
    y = kf_point;
end


% 回调函数
function break_blocking_callback(src, ~)
    % 将按钮的UserData属性设置为true，以便在主循环中检测到按下按钮的事件
    set(src, 'UserData', true);
end
