%% 完整的高级帧模式测试
clear; clc; close all;

fprintf('=== 完整高级帧模式测试 ===\n\n');

try
    %% 1. 测试MIMORADAR类属性
    fprintf('1. 测试MIMORADAR类属性...\n');
    fmcw = MIMORADAR;
    
    % 验证所有新属性存在
    assert(isprop(fmcw, 'advancedFrameMode'), '缺少属性: advancedFrameMode');
    assert(isprop(fmcw, 'radarFrequency2'), '缺少属性: radarFrequency2');
    assert(isprop(fmcw, 'chirpTRandEndime2'), '缺少属性: chirpTRandEndime2');
    assert(isprop(fmcw, 'Tidle2'), '缺少属性: Tidle2');
    assert(isprop(fmcw, 'sweepBw2'), '缺少属性: sweepBw2');
    assert(isprop(fmcw, 'Bw2'), '缺少属性: Bw2');
    assert(isprop(fmcw, 'NumOfChirpInOneSubframe2'), '缺少属性: NumOfChirpInOneSubframe2');
    assert(isprop(fmcw, 'activateTimeSubframe1'), '缺少属性: activateTimeSubframe1');
    assert(isprop(fmcw, 'activateTimeSubframe2'), '缺少属性: activateTimeSubframe2');
    assert(isprop(fmcw, 'subframeGap'), '缺少属性: subframeGap');
    
    fprintf('   ✓ 所有新属性验证成功\n');
    
    %% 2. 测试基本配置
    fprintf('2. 测试基本配置...\n');
    
    % 物理场景
    fmcw.propagationSpeed = physconst('LightSpeed');
    
    % 传统FMCW参数
    fmcw.radarFrequency = 77e9;
    fmcw.chirpTRandEndime = 56e-6;
    fmcw.Tidle = 14e-6;
    fmcw.sweepBw = 1e12;
    fmcw.Bw = fmcw.chirpTRandEndime * fmcw.sweepBw;
    fmcw.NumOfChirpInOneFrame = 64;
    
    fprintf('   ✓ 基本配置完成\n');
    
    %% 3. 测试高级帧模式配置
    fprintf('3. 测试高级帧模式配置...\n');
    
    % 启用高级帧模式
    fmcw.advancedFrameMode = true;
    
    % 第二子帧参数
    fmcw.radarFrequency2 = 77.5e9;
    fmcw.chirpTRandEndime2 = 60e-6;
    fmcw.Tidle2 = 12e-6;
    fmcw.sweepBw2 = 0.9e12;
    fmcw.Bw2 = fmcw.chirpTRandEndime2 * fmcw.sweepBw2;
    fmcw.NumOfChirpInOneSubframe2 = 64;
    
    % 时间计算
    fmcw.activateTimeSubframe1 = (fmcw.chirpTRandEndime + fmcw.Tidle) * fmcw.NumOfChirpInOneFrame;
    fmcw.activateTimeSubframe2 = (fmcw.chirpTRandEndime2 + fmcw.Tidle2) * fmcw.NumOfChirpInOneSubframe2;
    fmcw.activateTimeInOneFrame = fmcw.activateTimeSubframe1 + fmcw.activateTimeSubframe2;
    fmcw.subframeGap = 1e-6;
    fmcw.TimeOfFrame = fmcw.activateTimeInOneFrame + fmcw.subframeGap;
    
    fprintf('   ✓ 高级帧模式配置完成\n');
    
    %% 4. 显示配置信息
    fprintf('4. 配置信息:\n');
    fprintf('   高级帧模式: %s\n', mat2str(fmcw.advancedFrameMode));
    fprintf('   第一子帧: %.1f MHz带宽, %.1f us chirp时间, %d chirps\n', ...
        fmcw.Bw/1e6, fmcw.chirpTRandEndime*1e6, fmcw.NumOfChirpInOneFrame);
    fprintf('   第二子帧: %.1f MHz带宽, %.1f us chirp时间, %d chirps\n', ...
        fmcw.Bw2/1e6, fmcw.chirpTRandEndime2*1e6, fmcw.NumOfChirpInOneSubframe2);
    fprintf('   总帧时间: %.2f ms\n', fmcw.TimeOfFrame*1000);
    fprintf('   激活时间比例: %.1f%%\n', (fmcw.activateTimeInOneFrame/fmcw.TimeOfFrame)*100);
    
    %% 5. 测试天线配置
    fprintf('5. 测试天线配置...\n');
    fmcw.numberOfTransmitAntennas = 2;
    fmcw.numberOfReceiverAntennas = 4;
    fmcw.numberOfvirtueRxannate = fmcw.numberOfTransmitAntennas * fmcw.numberOfReceiverAntennas;
    fmcw.lambda = fmcw.propagationSpeed/fmcw.radarFrequency;
    
    fprintf('   ✓ 天线配置完成\n');
    
    %% 6. 测试数据矩阵大小计算
    fprintf('6. 测试数据矩阵大小计算...\n');
    fmcw.NumOfFrame = 5; % 测试用小数值
    
    if fmcw.advancedFrameMode
        total_chirps_per_frame = fmcw.NumOfChirpInOneFrame + fmcw.NumOfChirpInOneSubframe2;
        fprintf('   每帧总chirps: %d (子帧1: %d + 子帧2: %d)\n', ...
            total_chirps_per_frame, fmcw.NumOfChirpInOneFrame, fmcw.NumOfChirpInOneSubframe2);
        
        % 模拟数据矩阵
        test_matrix_size = [fmcw.NumOfFrame, total_chirps_per_frame, fmcw.numberOfvirtueRxannate, 268];
        fprintf('   预期数据矩阵大小: [%d, %d, %d, %d]\n', test_matrix_size);
        
        % 验证矩阵可以创建
        test_matrix = zeros(test_matrix_size);
        fprintf('   ✓ 数据矩阵创建成功，大小: [%d, %d, %d, %d]\n', size(test_matrix));
    end
    
    %% 7. 测试时间轴计算
    fprintf('7. 测试时间轴计算...\n');
    tt = [];
    for frame=1:2  % 只测试前2帧
        if fmcw.advancedFrameMode
            % 第一子帧
            for chirp=1:fmcw.NumOfChirpInOneFrame
                t = (frame-1)*fmcw.TimeOfFrame + (chirp-1)*(fmcw.chirpTRandEndime + fmcw.Tidle) * fmcw.numberOfTransmitAntennas;
                tt = [tt, t];
            end
            
            % 第二子帧
            subframe2_start_time = fmcw.activateTimeSubframe1 + fmcw.subframeGap;
            for chirp=1:fmcw.NumOfChirpInOneSubframe2
                t = (frame-1)*fmcw.TimeOfFrame + subframe2_start_time + (chirp-1)*(fmcw.chirpTRandEndime2 + fmcw.Tidle2) * fmcw.numberOfTransmitAntennas;
                tt = [tt, t];
            end
        end
    end
    
    fprintf('   前2帧时间点数量: %d\n', length(tt));
    fprintf('   时间范围: %.6f s 到 %.6f s\n', min(tt), max(tt));
    fprintf('   ✓ 时间轴计算成功\n');
    
    fprintf('\n=== 所有测试通过！高级帧模式配置正确 ===\n');
    fprintf('现在可以运行完整的 Copy_of_golf_of_MIMO_of_main.m 程序\n');
    
catch ME
    fprintf('❌ 测试失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('   位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end
