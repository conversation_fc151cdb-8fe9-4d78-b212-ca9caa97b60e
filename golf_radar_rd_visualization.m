% =========================================================================
%           高尔夫球场雷达监控系统 - FMCW雷达立方体与RD图可视化
% =========================================================================
%
% 功能:
% 1. 基于Copy_of_golf_of_MIMO_of_main.m的FMCW雷达信号处理
% 2. 实时生成4个雷达系统的雷达立方体数据
% 3. 计算并显示Range-Doppler图
% 4. 与高尔夫球轨迹仿真完全同步
% 5. 多窗口实时可视化
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

% --- 高尔夫球场布局参数 ---
n = 5;                      % 击球区域数量
tee_spacing = 10;           % 击球区域之间的间距 (m)
course_length = 250;        % 球场总长度 (m)
course_width = 60;          % 球场总宽度 (m)
tee_area_size = 8;          % 每个击球区域的大小 (m)

% --- 主雷达系统参数（后端） ---
main_radar_count = 3;       % 主雷达数量（左、中、右）
main_radar_height = 15;     % 主雷达安装高度 (m)
main_radar_distance = -20;  % 主雷达距离球场起点的距离 (m)
main_radar_spacing = 25;    % 主雷达之间的间距 (m)
main_radar_tilt = -15;      % 主雷达俯视角度 (度)
main_radar_inward_angle = 10; % 左右雷达向内倾斜角度 (度)

% --- 子雷达系统参数（中段监控） ---
sub_radar_distance = 50;    % 子雷达距离起点的距离 (m)
sub_radar_height = 8;       % 子雷达安装高度 (m)
sub_radar_coverage = 5;     % 每个子雷达覆盖的击球区域数量
sub_radar_count = ceil(n / sub_radar_coverage); % 子雷达数量

% --- FMCW雷达参数（基于Copy_of_golf_of_MIMO_of_main.m） ---
radar_frequency = 77e9;     % 载频 (Hz)
propagation_speed = physconst('LightSpeed'); % 光速
chirp_time = 56e-6;         % 线性调频上升时间 (s)
idle_time = 14e-6;          % chirp空闲时间 (s)
sweep_slope = 1e12;         % 调频斜率 (Hz/s)
sweep_bandwidth = chirp_time * sweep_slope; % 调频带宽
num_chirps_per_frame = 64;  % 每帧chirp数量
frame_time = 40e-3;         % 帧时间 (s)
adc_start_time = 2e-6;      % ADC启动时间 (s)
adc_sampling_freq = 5e6;    % ADC采样频率 (Hz)
num_adc_samples = 268;      % ADC采样点数

% --- 天线参数 ---
num_tx_antennas = 2;        % 发射天线数量
num_rx_antennas = 4;        % 接收天线数量
num_virtual_antennas = num_tx_antennas * num_rx_antennas; % 虚拟天线数量
lambda = propagation_speed / radar_frequency; % 波长

% --- 高尔夫球物理参数 ---
min_v0 = 36;            % 最小初始速度 (m/s)
max_v0 = 100;           % 最大初始速度 (m/s)
min_angle = 9;          % 最小发射仰角 (度)
max_angle = 14;         % 最大发射仰角 (度)
min_azimuth = -3.0;     % 最小水平偏角 (度)
max_azimuth = 3.0;      % 最大水平偏角 (度)
max_launch_delay = 2.0; % 最大发射延迟时间 (s)
dt = 0.01;              % 时间步长 (s)

% --- 物理常量 ---
g = 9.81;               % 重力加速度 (m/s^2)
m = 0.0459;             % 高尔夫球质量 (kg)
r = 0.02135;            % 高尔夫球半径 (m)
A = pi * r^2;           % 高尔夫球横截面积 (m^2)
rho = 1.225;            % 空气密度 (kg/m^3)
Cd = 0.4;               % 阻力系数

% --- RD图处理参数 ---
range_fft_size = 512;   % 距离FFT大小
vel_fft_size = 128;     % 速度FFT大小
rd_update_interval = 5; % RD图更新间隔（帧数）

%% 2. 创建高尔夫球场3D布局
fprintf('正在创建高尔夫球场3D布局...\n');

% 计算击球区域位置
tee_positions = zeros(n, 3);
for i = 1:n
    tee_positions(i, :) = [(i-1) * tee_spacing - (n-1)*tee_spacing/2, 0, 0];
end

% 计算主雷达位置
main_radar_positions = zeros(main_radar_count, 3);
for i = 1:main_radar_count
    x_offset = (i-2) * main_radar_spacing;
    main_radar_positions(i, :) = [x_offset, main_radar_distance, main_radar_height];
end

% 计算子雷达位置
sub_radar_positions = zeros(sub_radar_count, 3);
for i = 1:sub_radar_count
    coverage_center = (i-1) * sub_radar_coverage + sub_radar_coverage/2;
    if coverage_center > n
        coverage_center = n - sub_radar_coverage/2;
    end
    x_pos = (coverage_center - 1) * tee_spacing - (n-1)*tee_spacing/2;
    sub_radar_positions(i, :) = [x_pos, sub_radar_distance, sub_radar_height];
end

% 合并所有雷达位置
total_radars = main_radar_count + sub_radar_count;
all_radar_positions = [main_radar_positions; sub_radar_positions];
radar_names = {'左侧主雷达', '中央主雷达', '右侧主雷达', '子雷达1'};

fprintf('击球区域数量: %d\n', n);
fprintf('雷达系统总数: %d\n', total_radars);
fprintf('球场布局创建完成。\n\n');

%% 3. 初始化高尔夫球参数
fprintf('正在初始化高尔夫球参数...\n');

% 预分配结构体数组
balls(n) = struct('id', [], 'pos', [], 'vel', [], 'path', [], ...
                  'launch_time', [], 'launch_angle', [], 'launch_speed', [], 'azimuth', [], ...
                  'is_active', false, 'has_launched', false);

% 为每个球随机生成参数
fprintf('----------- 高尔夫球初始参数 -----------\n');
for i = 1:n
    balls(i).id = i;

    % 随机参数
    elevation_deg = min_angle + (max_angle - min_angle) * rand();
    elevation_rad = deg2rad(elevation_deg);
    balls(i).launch_angle = elevation_deg;

    azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();
    azimuth_rad = deg2rad(azimuth_deg);
    balls(i).azimuth = azimuth_deg;

    balls(i).launch_time = max_launch_delay * rand();

    v0_rand = min_v0 + (max_v0 - min_v0) * rand();
    balls(i).launch_speed = v0_rand;

    % 物理状态
    balls(i).pos = [tee_positions(i, 1); tee_positions(i, 2); 1.0];

    % 速度分解
    v_xy = v0_rand * cos(elevation_rad);
    v_z = v0_rand * sin(elevation_rad);
    v_x = v_xy * sin(azimuth_rad);
    v_y = v_xy * cos(azimuth_rad);
    balls(i).vel = [v_x; v_y; v_z];

    % 初始化轨迹和状态
    balls(i).path = [];
    balls(i).is_active = false;
    balls(i).has_launched = false;

    fprintf('球 %d: 时间=%.2fs, 速度=%.2fm/s, 仰角=%.2f°, 偏角=%.2f°\n', ...
            i, balls(i).launch_time, balls(i).launch_speed, balls(i).launch_angle, balls(i).azimuth);
end
fprintf('------------------------------------------\n\n');

%% 4. 初始化FMCW雷达系统
fprintf('正在初始化FMCW雷达系统...\n');

% 为每个雷达创建FMCW处理器
radar_processors = cell(total_radars, 1);
for i = 1:total_radars
    radar_processors{i} = create_fmcw_processor(all_radar_positions(i, :), ...
        radar_frequency, sweep_bandwidth, chirp_time, idle_time, ...
        num_chirps_per_frame, num_adc_samples, adc_sampling_freq, ...
        num_tx_antennas, num_rx_antennas, lambda);
end

% 初始化雷达数据立方体存储
frame_counter = 0;
max_frames = 1000; % 最大存储帧数
radar_data_cubes = cell(total_radars, 1);
for i = 1:total_radars
    radar_data_cubes{i} = zeros(max_frames, num_chirps_per_frame, num_virtual_antennas, num_adc_samples);
end

fprintf('FMCW雷达系统初始化完成。\n');

%% 5. 创建可视化窗口
fprintf('正在创建可视化窗口...\n');

% 主窗口 - 3D场景
main_fig = figure('Name', '高尔夫球场雷达监控系统 - 3D场景', 'NumberTitle', 'off', ...
                  'Position', [50, 100, 800, 600], 'Color', 'w');

% RD图窗口
rd_fig = figure('Name', '实时Range-Doppler图', 'NumberTitle', 'off', ...
                'Position', [900, 100, 1000, 800], 'Color', 'w');

% 在主窗口中设置3D场景
figure(main_fig);
hold on;
grid on;
box on;

xlabel('X 轴 - 横向位置 (m)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Y 轴 - 纵向距离 (m)', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Z 轴 - 高度 (m)', 'FontSize', 12, 'FontWeight', 'bold');

view(45, 20);
axis equal;
set(gca, 'FontSize', 10);

% 设置坐标轴范围
x_range = [-course_width/2, course_width/2];
y_range = [main_radar_distance-10, course_length];
z_range = [0, main_radar_height+5];
xlim(x_range);
ylim(y_range);
zlim(z_range);

% 绘制球场基础设施
draw_golf_course(x_range, y_range, tee_positions, tee_area_size, n);

% 绘制雷达系统
draw_radar_systems(all_radar_positions, radar_names, total_radars);

% 初始化球轨迹绘图对象
path_plots = gobjects(1, n);
ball_markers = gobjects(1, n);
colors = lines(n);

for i = 1:n
    path_plots(i) = plot3(NaN, NaN, NaN, '-', 'Color', colors(i,:), 'LineWidth', 2);
    ball_markers(i) = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', colors(i,:), ...
                            'MarkerEdgeColor', 'k', 'MarkerSize', 8);
end

title('高尔夫球场雷达监控系统 - 3D场景与轨迹', 'FontSize', 14, 'FontWeight', 'bold');

% 在RD图窗口中设置子图
figure(rd_fig);
rd_subplots = gobjects(2, 2);
rd_images = gobjects(2, 2);

for i = 1:total_radars
    row = ceil(i/2);
    col = mod(i-1, 2) + 1;
    rd_subplots(row, col) = subplot(2, 2, i);
    rd_images(row, col) = imagesc([], [], zeros(vel_fft_size, range_fft_size/2));
    colorbar;
    colormap(rd_subplots(row, col), 'jet');
    title(radar_names{i}, 'FontSize', 12, 'FontWeight', 'bold');
    xlabel('距离 (m)', 'FontSize', 10);
    ylabel('速度 (m/s)', 'FontSize', 10);
    axis xy;
    grid on;
end

fprintf('可视化窗口创建完成。\n\n');

%% 6. 动态仿真主循环
fprintf('正在开始动态轨迹仿真与雷达数据处理...\n');
fprintf('仿真开始... 按 Ctrl+C 停止。\n');

sim_time = 0;
num_finished_balls = 0;
frame_counter = 0;

% 计算RD图坐标轴
range_res = propagation_speed / (2 * sweep_bandwidth);
max_range = range_res * range_fft_size / 2;
range_bins = linspace(0, max_range, range_fft_size/2);

vel_res = propagation_speed / (2 * radar_frequency * (chirp_time + idle_time) * num_chirps_per_frame);
max_vel = vel_res * vel_fft_size / 2;
vel_bins = linspace(-max_vel, max_vel, vel_fft_size);

while num_finished_balls < n

    % --- 更新每个球的状态 ---
    for i = 1:n
        % 检查是否到了发射时间且尚未发射
        if sim_time >= balls(i).launch_time && ~balls(i).has_launched
            balls(i).has_launched = true;
            balls(i).is_active = true;
            balls(i).path = balls(i).pos;
            fprintf('球 %d 发射！时间: %.2fs\n', i, sim_time);
        end

        % 如果球在空中，则更新其物理状态
        if balls(i).is_active
            % 计算物理量
            v_mag = norm(balls(i).vel);
            F_drag = -0.5 * rho * A * Cd * v_mag * balls(i).vel;
            F_net = [0; 0; -m*g] + F_drag;
            a = F_net / m;

            % 更新速度和位置
            balls(i).vel = balls(i).vel + a * dt;
            balls(i).pos = balls(i).pos + balls(i).vel * dt;

            % 记录轨迹点
            balls(i).path = [balls(i).path, balls(i).pos];

            % 检查是否落地
            if balls(i).pos(3) < 0
                balls(i).is_active = false;
                num_finished_balls = num_finished_balls + 1;
                fprintf('球 %d 落地！飞行时间: %.2fs, 距离: %.1fm\n', ...
                        i, sim_time - balls(i).launch_time, balls(i).pos(2));
            end
        end
    end

    % --- 收集活跃球的位置 ---
    active_ball_positions = [];
    for i = 1:n
        if balls(i).is_active
            active_ball_positions = [active_ball_positions; balls(i).pos'];
        end
    end

    % --- 雷达数据处理 ---
    if ~isempty(active_ball_positions) && mod(frame_counter, rd_update_interval) == 0
        frame_counter = frame_counter + 1;
        current_frame = mod(frame_counter - 1, max_frames) + 1;

        % 为每个雷达生成数据立方体
        for radar_idx = 1:total_radars
            radar_pos = all_radar_positions(radar_idx, :);

            % 生成当前帧的雷达数据立方体
            frame_data = generate_radar_cube(radar_pos, active_ball_positions, ...
                radar_processors{radar_idx}, num_chirps_per_frame, num_virtual_antennas, num_adc_samples);

            % 存储数据立方体
            radar_data_cubes{radar_idx}(current_frame, :, :, :) = frame_data;

            % 生成RD图
            [rd_map, ~, ~] = generate_rd_map(squeeze(frame_data), range_fft_size, vel_fft_size);

            % 更新RD图显示
            row = ceil(radar_idx/2);
            col = mod(radar_idx-1, 2) + 1;

            figure(rd_fig);
            subplot(2, 2, radar_idx);
            imagesc(range_bins, vel_bins, rd_map);
            axis xy;
            colorbar;
            colormap('jet');
            title(sprintf('%s - 帧 %d', radar_names{radar_idx}, frame_counter), 'FontSize', 12);
            xlabel('距离 (m)', 'FontSize', 10);
            ylabel('速度 (m/s)', 'FontSize', 10);
            grid on;
            caxis([-80, -20]); % 设置颜色范围
        end
    else
        frame_counter = frame_counter + 1;
    end

    % --- 更新3D场景绘图 ---
    figure(main_fig);
    for i = 1:n
        if balls(i).has_launched
            % 更新路径线条
            set(path_plots(i), 'XData', balls(i).path(1,:), ...
                               'YData', balls(i).path(2,:), ...
                               'ZData', balls(i).path(3,:));
            % 更新球的当前位置
            if balls(i).is_active
                set(ball_markers(i), 'XData', balls(i).pos(1), ...
                                     'YData', balls(i).pos(2), ...
                                     'ZData', balls(i).pos(3));
            else
                % 球落地后显示最终位置
                final_pos = balls(i).path(:, end);
                final_pos(3) = 0;
                set(ball_markers(i), 'XData', final_pos(1), ...
                                     'YData', final_pos(2), ...
                                     'ZData', final_pos(3));
            end
        end
    end

    % 刷新画布
    drawnow limitrate;

    % 更新仿真时间
    sim_time = sim_time + dt;

    % 防止无限循环
    if sim_time > 30
        fprintf('仿真超时，强制结束。\n');
        break;
    end
end

fprintf('\n=== 仿真完成 ===\n');
fprintf('- 击球区域: %d 个\n', n);
fprintf('- 雷达系统: %d 个\n', total_radars);
fprintf('- 完成轨迹的球: %d 个\n', num_finished_balls);
fprintf('- 总仿真时间: %.2f 秒\n', sim_time);
fprintf('- 处理帧数: %d 帧\n', frame_counter);
fprintf('请查看RD图窗口观察雷达检测结果。\n');

%% 辅助函数定义区域

% 辅助函数：创建FMCW处理器
function processor = create_fmcw_processor(radar_pos, freq, bandwidth, chirp_time, idle_time, ...
    num_chirps, num_samples, sampling_freq, num_tx, num_rx, lambda)
    processor = struct();
    processor.position = radar_pos;
    processor.frequency = freq;
    processor.bandwidth = bandwidth;
    processor.chirp_time = chirp_time;
    processor.idle_time = idle_time;
    processor.num_chirps = num_chirps;
    processor.num_samples = num_samples;
    processor.sampling_freq = sampling_freq;
    processor.num_tx = num_tx;
    processor.num_rx = num_rx;
    processor.lambda = lambda;
    processor.sweep_slope = bandwidth / chirp_time;
end

% 辅助函数：生成雷达数据立方体
function radar_cube = generate_radar_cube(radar_pos, target_positions, processor, ...
    num_chirps, num_virtual_rx, num_samples)
    % 初始化雷达数据立方体
    radar_cube = zeros(num_chirps, num_virtual_rx, num_samples);

    if isempty(target_positions)
        return;
    end

    % 为每个chirp和每个目标计算信号
    for chirp_idx = 1:num_chirps
        for target_idx = 1:size(target_positions, 1)
            target_pos = target_positions(target_idx, :);

            % 计算距离
            distance = norm(target_pos - radar_pos);

            % 计算距离频率
            range_freq = -processor.sweep_slope * 2 / physconst('LightSpeed') * distance;

            % 计算相位
            phase = processor.frequency * 2 / physconst('LightSpeed') * distance;

            % 生成距离信号
            sample_times = (0:num_samples-1) / processor.sampling_freq;
            range_signal = exp(1j * 2 * pi * range_freq * sample_times);

            % 添加相位
            range_signal = range_signal * exp(1j * 2 * pi * phase);

            % 计算多普勒频率（需要目标速度信息）
            % 这里简化处理，实际应用中需要从球轨迹中获取速度
            doppler_freq = 0; % 2 * processor.frequency / physconst('LightSpeed') * radial_velocity
            doppler_phase = doppler_freq * (chirp_idx - 1) * (processor.chirp_time + processor.idle_time);

            % 将信号添加到所有虚拟接收天线
            for rx_idx = 1:num_virtual_rx
                radar_cube(chirp_idx, rx_idx, :) = squeeze(radar_cube(chirp_idx, rx_idx, :))' + ...
                    range_signal * exp(1j * doppler_phase * chirp_idx);
            end
        end
    end

    % 添加噪声
    noise_power = 0.001;
    noise_real = sqrt(noise_power/2) * randn(size(radar_cube));
    noise_imag = sqrt(noise_power/2) * randn(size(radar_cube));
    noise = complex(noise_real, noise_imag);
    radar_cube = radar_cube + noise;
end

% 辅助函数：生成Range-Doppler图
function [rd_map, range_bins, vel_bins] = generate_rd_map(frame_data, range_fft_size, vel_fft_size)
    % frame_data: [chirps, rx, samples]
    [num_chirps, num_rx, num_samples] = size(frame_data);

    % 距离FFT (对samples维度)
    range_window = repmat(hamming(num_samples)', [num_chirps, num_rx, 1]);
    windowed_data = frame_data .* range_window;

    % 距离FFT
    range_fft = fft(windowed_data, range_fft_size, 3);
    range_fft = range_fft(:, :, 1:range_fft_size/2); % 只取正频率部分

    % 速度FFT (对chirps维度)
    vel_window = repmat(hamming(num_chirps), [1, num_rx, range_fft_size/2]);
    windowed_range_data = range_fft .* vel_window;

    % 速度FFT
    vel_fft = fft(windowed_range_data, vel_fft_size, 1);
    vel_fft = fftshift(vel_fft, 1);

    % 多天线合并
    rd_map = squeeze(sum(abs(vel_fft).^2, 2));

    % 转换为dB
    rd_map = 10 * log10(rd_map + eps);

    % 生成坐标轴（占位符，实际在主函数中计算）
    range_bins = 1:range_fft_size/2;
    vel_bins = 1:vel_fft_size;
end

% 辅助函数：绘制高尔夫球场
function draw_golf_course(x_range, y_range, tee_positions, tee_area_size, n)
    % 绘制球场地面
    [X_ground, Y_ground] = meshgrid(x_range(1):5:x_range(2), y_range(1):5:y_range(2));
    Z_ground = zeros(size(X_ground));
    surf(X_ground, Y_ground, Z_ground, 'FaceColor', [0.2, 0.8, 0.2], ...
         'FaceAlpha', 0.3, 'EdgeColor', 'none');

    % 绘制击球区域
    for i = 1:n
        pos = tee_positions(i, :);
        x_tee = pos(1) + [-tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2, -tee_area_size/2];
        y_tee = pos(2) + [-tee_area_size/2, -tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2];
        z_tee = [0.1, 0.1, 0.1, 0.1, 0.1];

        plot3(x_tee, y_tee, z_tee, 'k-', 'LineWidth', 2);
        fill3(x_tee(1:4), y_tee(1:4), z_tee(1:4), [0.8, 0.6, 0.4], 'FaceAlpha', 0.7);

        text(pos(1), pos(2), pos(3)+2, sprintf('击球区 %d', i), ...
             'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
    end
end

% 辅助函数：绘制雷达系统
function draw_radar_systems(radar_positions, radar_names, total_radars)
    radar_colors = [1, 0, 0; 0, 0, 1; 0, 1, 0; 1, 0.5, 0]; % 红、蓝、绿、橙
    radar_size = [2, 1.5, 1];

    for i = 1:total_radars
        pos = radar_positions(i, :);
        color = radar_colors(i, :);

        % 绘制雷达设备本体
        draw_radar_box(pos, radar_size, color, 0.8);

        % 添加雷达标签
        text(pos(1), pos(2), pos(3)+radar_size(3)+1, radar_names{i}, ...
             'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold', ...
             'Color', color);

        % 绘制支撑杆
        plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
              'Color', [0.5, 0.5, 0.5], 'LineWidth', 3);
    end
end

% 辅助函数：绘制雷达设备长方体
function draw_radar_box(center, size, color, alpha)
    x = center(1) + [-size(1)/2, size(1)/2];
    y = center(2) + [-size(2)/2, size(2)/2];
    z = center(3) + [-size(3)/2, size(3)/2];

    vertices = [
        x(1), y(1), z(1); x(2), y(1), z(1); x(2), y(2), z(1); x(1), y(2), z(1);
        x(1), y(1), z(2); x(2), y(1), z(2); x(2), y(2), z(2); x(1), y(2), z(2);
    ];

    faces = [
        1, 2, 3, 4; 5, 6, 7, 8; 1, 2, 6, 5;
        3, 4, 8, 7; 1, 4, 8, 5; 2, 3, 7, 6;
    ];

    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', alpha, ...
          'EdgeColor', 'k', 'LineWidth', 1);
end
