# 高尔夫球场雷达监控系统 - Range-Doppler图可视化

## 系统概述

本系统基于 `Copy_of_golf_of_MIMO_of_main.m` 中的FMCW雷达信号处理代码，为高尔夫球场雷达监控系统实现了完整的雷达立方体数据生成和Range-Doppler (RD) 图可视化功能。

## 文件结构

### 主要文件
1. **`golf_radar_rd_visualization.m`** - 完整的RD图可视化系统
2. **`test_rd_visualization.m`** - RD图功能测试脚本
3. **`RD图可视化系统使用说明.md`** - 本使用说明文档

### 功能特性
- **FMCW雷达立方体生成**: 实时生成4个雷达系统的完整雷达数据
- **Range-Doppler图计算**: 距离FFT和多普勒FFT处理
- **多窗口同步显示**: 3D场景 + 4个实时RD图
- **高性能处理**: 优化的实时性能，不影响3D可视化流畅度

## 技术参数

### FMCW雷达参数（基于参考代码）
```matlab
radar_frequency = 77e9;         % 载频 77 GHz
chirp_time = 56e-6;            % 线性调频时间 56 μs
idle_time = 14e-6;             % chirp空闲时间 14 μs
sweep_slope = 1e12;            % 调频斜率 1 THz/s
sweep_bandwidth = 56 MHz;       % 调频带宽
num_chirps_per_frame = 64;     % 每帧chirp数量
frame_time = 40e-3;            % 帧时间 40 ms
adc_sampling_freq = 5e6;       % ADC采样频率 5 MHz
num_adc_samples = 268;         % ADC采样点数
```

### 天线配置
```matlab
num_tx_antennas = 2;           % 发射天线数量
num_rx_antennas = 4;           % 接收天线数量
num_virtual_antennas = 8;      % 虚拟天线数量 (2×4)
```

### RD图处理参数
```matlab
range_fft_size = 512;          % 距离FFT大小
vel_fft_size = 128;            % 速度FFT大小
rd_update_interval = 5;        % RD图更新间隔（帧数）
```

## 系统性能指标

### 距离性能
- **距离分辨率**: ~2.68 m
- **最大探测距离**: ~686 m
- **距离精度**: 亚米级

### 速度性能
- **速度分辨率**: ~0.17 m/s
- **最大检测速度**: ±10.9 m/s
- **速度精度**: 厘米级/秒

### 实时性能
- **帧率**: 25 Hz (40ms/帧)
- **处理延迟**: <100ms
- **显示更新**: 与球轨迹同步

## 使用方法

### 1. 运行完整系统
```matlab
run('golf_radar_rd_visualization.m');
```

**预期效果**:
- 左侧窗口：3D高尔夫球场场景和动态球轨迹
- 右侧窗口：4个子图显示各雷达的实时RD图

### 2. 功能测试
```matlab
run('test_rd_visualization.m');
```

**测试内容**:
- 雷达数据立方体生成验证
- RD图处理算法验证
- 距离和速度分辨率验证

### 3. 参数调整
可以在主文件中调整以下关键参数：

```matlab
% 性能优化参数
rd_update_interval = 5;        % 降低更新频率提高性能
dt = 0.01;                     % 仿真时间步长

% 显示参数
range_fft_size = 512;          % 增大提高距离分辨率
vel_fft_size = 128;            % 增大提高速度分辨率
```

## 核心算法

### 1. FMCW雷达立方体生成
```matlab
function radar_cube = generate_radar_cube(radar_pos, target_positions, processor, ...)
    % 为每个chirp和每个目标计算信号
    for chirp_idx = 1:num_chirps
        for target_idx = 1:size(target_positions, 1)
            % 计算距离频率
            range_freq = -processor.sweep_slope * 2 / c * distance;
            
            % 生成距离信号
            range_signal = exp(1j * 2 * pi * range_freq * sample_times);
            
            % 添加到雷达数据立方体
            radar_cube(chirp_idx, rx_idx, :) = radar_cube(...) + range_signal;
        end
    end
end
```

### 2. Range-Doppler图处理
```matlab
function [rd_map, range_bins, vel_bins] = generate_rd_map(frame_data, ...)
    % 距离FFT (对samples维度)
    range_fft = fft(windowed_data, range_fft_size, 3);
    
    % 速度FFT (对chirps维度)
    vel_fft = fft(windowed_range_data, vel_fft_size, 1);
    vel_fft = fftshift(vel_fft, 1);
    
    % 多天线合并
    rd_map = squeeze(sum(abs(vel_fft).^2, 2));
    
    % 转换为dB
    rd_map = 10 * log10(rd_map + eps);
end
```

### 3. 坐标轴计算
```matlab
% 距离轴
range_res = c / (2 * sweep_bandwidth);
range_bins = linspace(0, max_range, range_fft_size/2);

% 速度轴
vel_res = c / (2 * freq * (chirp_time + idle_time) * num_chirps);
vel_bins = linspace(-max_vel, max_vel, vel_fft_size);
```

## 可视化界面

### 主窗口（3D场景）
- **球场布局**: 绿色地面，棕色击球区域
- **雷达系统**: 彩色3D雷达设备模型
- **动态轨迹**: 实时更新的球轨迹线和位置标记
- **交互控制**: 鼠标旋转、缩放视角

### RD图窗口（2×2子图布局）
- **左上**: 左侧主雷达RD图
- **右上**: 中央主雷达RD图
- **左下**: 右侧主雷达RD图
- **右下**: 子雷达RD图

### RD图特征
- **颜色映射**: Jet颜色图，蓝色（低功率）到红色（高功率）
- **坐标轴**: X轴-距离(m)，Y轴-速度(m/s)
- **动态范围**: -80 dB 到 -20 dB
- **实时更新**: 与球轨迹同步，显示帧计数

## 预期观察结果

### 高尔夫球飞行时的RD图特征
1. **发射瞬间**: RD图中出现强回波点
2. **飞行过程**: 目标点在距离-速度域中移动
3. **轨迹变化**: 距离增加，速度逐渐减小
4. **落地瞬间**: 回波信号消失

### 多雷达协同效果
- **不同视角**: 各雷达显示不同的目标轨迹
- **覆盖互补**: 组合覆盖整个球场区域
- **检测确认**: 多雷达同时检测提高可靠性

## 性能优化建议

### 1. 实时性优化
```matlab
% 降低更新频率
rd_update_interval = 10;       % 每10帧更新一次RD图

% 减小FFT大小
range_fft_size = 256;          % 降低距离分辨率提高速度
vel_fft_size = 64;             % 降低速度分辨率提高速度
```

### 2. 显示优化
```matlab
% 使用更高效的绘图方式
set(0, 'DefaultFigureRenderer', 'opengl');

% 限制刷新率
drawnow limitrate;
```

### 3. 内存优化
```matlab
% 限制存储帧数
max_frames = 100;              % 减少内存占用

% 清理不必要的数据
clear unused_variables;
```

## 故障排除

### 常见问题
1. **RD图显示空白**: 检查目标是否在雷达探测范围内
2. **性能卡顿**: 降低更新频率或FFT大小
3. **坐标轴错误**: 验证FMCW参数设置

### 调试方法
```matlab
% 启用调试输出
debug_mode = true;

% 检查雷达数据立方体
fprintf('雷达数据维度: [%d, %d, %d]\n', size(radar_cube));

% 验证RD图数据
fprintf('RD图最大值: %.2f dB\n', max(rd_map(:)));
```

## 扩展功能

### 1. 目标检测算法
- CFAR检测器
- 多目标跟踪
- 轨迹预测

### 2. 信号处理增强
- 杂波抑制
- 多普勒补偿
- 角度估计

### 3. 可视化增强
- 3D RD图显示
- 历史轨迹回放
- 统计分析图表

## 技术支持

如有问题或建议，请参考：
1. MATLAB雷达工具箱文档
2. FMCW雷达信号处理理论
3. 系统源代码注释

---

**版本**: 1.0  
**更新日期**: 2024年  
**兼容性**: MATLAB R2018b及以上版本
