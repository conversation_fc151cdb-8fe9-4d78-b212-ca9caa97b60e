% =========================================================================
%           高尔夫球场雷达监控系统3D可视化 + 动态球轨迹仿真
% =========================================================================
%
% 功能:
% 1. 创建高尔夫球场3D布局，包含n个击球区域（可扩展至10个）
% 2. 在球场后方安装3个MIMO雷达系统（左、中、右）
% 3. 在球场中段安装子雷达监控系统
% 4. 可视化雷达覆盖区域和监控范围
% 5. 动态仿真高尔夫球发射轨迹
% 6. 实时显示雷达监控效果
% 7. 提供完整的3D沉浸式可视化体验
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

% --- 高尔夫球场布局参数 ---
n = 5;                      % 击球区域数量 (可扩展至10个)
tee_spacing = 10;           % 击球区域之间的间距 (m)
course_length = 250;        % 球场总长度 (m)
course_width = 60;          % 球场总宽度 (m)
tee_area_size = 8;          % 每个击球区域的大小 (m)

% --- 主雷达系统参数（后端） ---
main_radar_count = 3;       % 主雷达数量（左、中、右）
main_radar_height = 15;     % 主雷达安装高度 (m)
main_radar_distance = -20;  % 主雷达距离球场起点的距离 (m，负数表示在后方)
main_radar_spacing = 25;    % 主雷达之间的间距 (m)
main_radar_tilt = -15;      % 主雷达俯视角度 (度)
main_radar_inward_angle = 10; % 左右雷达向内倾斜角度 (度)

% --- 子雷达系统参数（中段监控） ---
sub_radar_distance = 50;    % 子雷达距离起点的距离 (m)
sub_radar_height = 8;       % 子雷达安装高度 (m)
sub_radar_coverage = 5;     % 每个子雷达覆盖的击球区域数量
sub_radar_count = ceil(n / sub_radar_coverage); % 子雷达数量

% --- 雷达覆盖区域参数 ---
main_radar_range = 200;     % 主雷达最大探测距离 (m)
main_radar_fov_h = 60;      % 主雷达水平视场角 (度)
main_radar_fov_v = 30;      % 主雷达垂直视场角 (度)
sub_radar_range = 80;       % 子雷达最大探测距离 (m)
sub_radar_fov_h = 45;       % 子雷达水平视场角 (度)
sub_radar_fov_v = 25;       % 子雷达垂直视场角 (度)

% --- 高尔夫球物理参数 ---
min_v0 = 36;            % 最小初始速度 (m/s)
max_v0 = 100;           % 最大初始速度 (m/s)
min_angle = 9;          % 最小发射仰角 (度)
max_angle = 14;         % 最大发射仰角 (度)
min_azimuth = -3.0;     % 最小水平偏角 (度)，负数代表偏左
max_azimuth = 3.0;      % 最大水平偏角 (度)，正数代表偏右
max_launch_delay = 2.0; % 最大发射延迟时间 (s)
dt = 0.01;              % 时间步长 (s)，调整为更大值以提高性能

% --- 物理常量 ---
g = 9.81;               % 重力加速度 (m/s^2)
m = 0.0459;             % 高尔夫球质量 (kg)
r = 0.02135;            % 高尔夫球半径 (m)
A = pi * r^2;           % 高尔夫球横截面积 (m^2)
rho = 1.225;            % 空气密度 (kg/m^3)
Cd = 0.4;               % 阻力系数 (对于球体是典型值)

% --- 雷达信号处理参数 ---
radar_frequency = 77e9;     % 雷达载频 (Hz)
propagation_speed = 3e8;    % 光速 (m/s)
sweep_bandwidth = 4e9;      % 扫频带宽 (Hz)
chirp_time = 56e-6;         % 线性调频时间 (s)
num_adc_samples = 256;      % ADC采样点数
adc_sampling_freq = 10e6;   % ADC采样频率 (Hz)
noise_floor = -180;          % 噪声底 (dBm)
detection_threshold = -260;  % 检测门限 (dBm)

% --- 可视化参数 ---
coverage_alpha = 0.15;      % 覆盖区域透明度
radar_size = [2, 1.5, 1];   % 雷达设备尺寸 [长, 宽, 高] (m)
enable_radar_echo = true;   % 启用雷达回波计算

%% 2. 创建高尔夫球场3D布局
fprintf('正在创建高尔夫球场3D布局...\n');

% 计算击球区域位置
tee_positions = zeros(n, 3);
for i = 1:n
    tee_positions(i, :) = [(i-1) * tee_spacing - (n-1)*tee_spacing/2, 0, 0];
end

% 计算主雷达位置
main_radar_positions = zeros(main_radar_count, 3);
for i = 1:main_radar_count
    x_offset = (i-2) * main_radar_spacing; % 中心雷达在x=0，左右分布
    main_radar_positions(i, :) = [x_offset, main_radar_distance, main_radar_height];
end

% 计算子雷达位置
sub_radar_positions = zeros(sub_radar_count, 3);
for i = 1:sub_radar_count
    % 子雷达沿击球区域分布
    coverage_center = (i-1) * sub_radar_coverage + sub_radar_coverage/2;
    if coverage_center > n
        coverage_center = n - sub_radar_coverage/2;
    end
    x_pos = (coverage_center - 1) * tee_spacing - (n-1)*tee_spacing/2;
    sub_radar_positions(i, :) = [x_pos, sub_radar_distance, sub_radar_height];
end

fprintf('击球区域数量: %d\n', n);
fprintf('主雷达数量: %d\n', main_radar_count);
fprintf('子雷达数量: %d\n', sub_radar_count);
fprintf('球场布局创建完成。\n\n');

%% 2.5. 初始化高尔夫球参数
fprintf('正在初始化高尔夫球参数...\n');

% 预分配结构体数组以提高效率
balls(n) = struct('id', [], 'pos', [], 'vel', [], 'path', [], ...
                  'launch_time', [], 'launch_angle', [], 'launch_speed', [], 'azimuth', [], ...
                  'is_active', false, 'has_launched', false);

% 为每个球随机生成参数
fprintf('----------- 高尔夫球初始参数 -----------\n');
for i = 1:n
    % --- 基本参数 ---
    balls(i).id = i;

    % --- 随机参数 ---
    % 垂直发射角 (仰角)
    elevation_deg = min_angle + (max_angle - min_angle) * rand();
    elevation_rad = deg2rad(elevation_deg);
    balls(i).launch_angle = elevation_deg;

    % 水平发射角 (偏角/方位角)
    azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();
    azimuth_rad = deg2rad(azimuth_deg);
    balls(i).azimuth = azimuth_deg;

    balls(i).launch_time = max_launch_delay * rand();

    % 为每个球生成一个在指定范围内的随机速度
    v0_rand = min_v0 + (max_v0 - min_v0) * rand();
    balls(i).launch_speed = v0_rand;

    % --- 物理状态 ---
    balls(i).pos = [tee_positions(i, 1); tee_positions(i, 2); 1.0];

    % 使用三角函数将速度分解到X, Y, Z三个轴
    % 首先，将速度投影到水平面(v_xy)和垂直面(v_z)
    v_xy = v0_rand * cos(elevation_rad);
    v_z = v0_rand * sin(elevation_rad);

    % 然后，根据水平偏角(azimuth)将v_xy分解到vx和vy
    v_x = v_xy * sin(azimuth_rad);
    v_y = v_xy * cos(azimuth_rad);

    balls(i).vel = [v_x; v_y; v_z];

    % --- 初始化轨迹和状态标志 ---
    balls(i).path = []; % 轨迹历史初始化为空
    balls(i).is_active = false; % 初始状态为"未在空中"
    balls(i).has_launched = false; % 初始状态为"尚未发射"

    % --- 打印信息 ---
    fprintf('球 %d: 时间=%.2fs, 速度=%.2fm/s, 仰角=%.2f°, 偏角=%.2f°\n', ...
            i, balls(i).launch_time, balls(i).launch_speed, balls(i).launch_angle, balls(i).azimuth);
end
fprintf('------------------------------------------\n\n');

%% 3. 创建3D可视化环境
figure('Name', '高尔夫球场雷达监控系统3D可视化', 'NumberTitle', 'off', ...
       'Color', 'w', 'Position', [100, 100, 1200, 800]);
hold on;
grid on;
box on;

% 设置坐标轴
xlabel('X 轴 - 横向位置 (m)', 'FontSize', 12, 'FontWeight', 'bold');
ylabel('Y 轴 - 纵向距离 (m)', 'FontSize', 12, 'FontWeight', 'bold');
zlabel('Z 轴 - 高度 (m)', 'FontSize', 12, 'FontWeight', 'bold');

% 设置视角和比例
view(45, 20);
axis equal;
set(gca, 'FontSize', 10);

% 设置坐标轴范围
x_range = [-course_width/2, course_width/2];
y_range = [main_radar_distance-10, course_length];
z_range = [0, main_radar_height+5];
xlim(x_range);
ylim(y_range);
zlim(z_range);

%% 4. 绘制高尔夫球场基础设施
fprintf('正在绘制球场基础设施...\n');

% 绘制球场地面
[X_ground, Y_ground] = meshgrid(x_range(1):5:x_range(2), y_range(1):5:y_range(2));
Z_ground = zeros(size(X_ground));
surf(X_ground, Y_ground, Z_ground, 'FaceColor', [0.2, 0.8, 0.2], ...
     'FaceAlpha', 0.3, 'EdgeColor', 'none');

% 绘制击球区域
for i = 1:n
    pos = tee_positions(i, :);
    % 创建击球区域的方形平台
    x_tee = pos(1) + [-tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2, -tee_area_size/2];
    y_tee = pos(2) + [-tee_area_size/2, -tee_area_size/2, tee_area_size/2, tee_area_size/2, -tee_area_size/2];
    z_tee = [0.1, 0.1, 0.1, 0.1, 0.1];

    plot3(x_tee, y_tee, z_tee, 'k-', 'LineWidth', 2);
    fill3(x_tee(1:4), y_tee(1:4), z_tee(1:4), [0.8, 0.6, 0.4], 'FaceAlpha', 0.7);

    % 添加击球区域标签
    text(pos(1), pos(2), pos(3)+2, sprintf('击球区 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
end

fprintf('球场基础设施绘制完成。\n');

%% 5. 绘制主雷达系统
fprintf('正在绘制主雷达系统...\n');

main_radar_colors = [1, 0, 0; 0, 0, 1; 0, 1, 0]; % 红、蓝、绿
main_radar_names = {'左侧主雷达', '中央主雷达', '右侧主雷达'};

for i = 1:main_radar_count
    pos = main_radar_positions(i, :);
    color = main_radar_colors(i, :);

    % 绘制雷达设备本体（长方体）
    draw_radar_box(pos, radar_size, color, 0.8);

    % 添加雷达标签
    text(pos(1), pos(2), pos(3)+radar_size(3)+1, main_radar_names{i}, ...
         'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold', ...
         'Color', color);

    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 3);
end

fprintf('主雷达系统绘制完成。\n');

%% 6. 绘制子雷达系统
fprintf('正在绘制子雷达系统...\n');

sub_radar_color = [1, 0.5, 0]; % 橙色

for i = 1:sub_radar_count
    pos = sub_radar_positions(i, :);

    % 绘制子雷达设备本体（较小的长方体）
    sub_size = radar_size * 0.7; % 子雷达比主雷达小30%
    draw_radar_box(pos, sub_size, sub_radar_color, 0.8);

    % 添加子雷达标签
    text(pos(1), pos(2), pos(3)+sub_size(3)+1, sprintf('子雷达 %d', i), ...
         'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold', ...
         'Color', sub_radar_color);

    % 绘制支撑杆
    plot3([pos(1), pos(1)], [pos(2), pos(2)], [0, pos(3)], ...
          'Color', [0.5, 0.5, 0.5], 'LineWidth', 2);
end

fprintf('子雷达系统绘制完成。\n');

%% 7. 初始化球轨迹绘图对象
fprintf('正在初始化轨迹绘图对象...\n');

% 为每个球的路径和当前位置创建绘图句柄
path_plots = gobjects(1, n);
ball_markers = gobjects(1, n);
colors = lines(n); % 为每个球生成不同的颜色

for i = 1:n
    % 路径线条 (初始为空)
    path_plots(i) = plot3(NaN, NaN, NaN, '-', 'Color', colors(i,:), 'LineWidth', 2);
    % 球的当前位置标记
    ball_markers(i) = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', colors(i,:), ...
                            'MarkerEdgeColor', 'k', 'MarkerSize', 8);
end

fprintf('轨迹绘图对象初始化完成。\n');

%% 7.5. 初始化雷达回波数据结构
if enable_radar_echo
    fprintf('正在初始化雷达回波数据结构...\n');

    % 创建雷达系统结构
    total_radars = main_radar_count + sub_radar_count;
    radar_systems = struct();

    % 初始化主雷达系统
    for i = 1:main_radar_count
        radar_systems(i).id = i;
        radar_systems(i).type = 'main';
        radar_systems(i).name = main_radar_names{i};
        radar_systems(i).position = main_radar_positions(i, :);
        radar_systems(i).range = main_radar_range;
        radar_systems(i).fov_h = main_radar_fov_h;
        radar_systems(i).fov_v = main_radar_fov_v;
        radar_systems(i).tilt = main_radar_tilt;
        if i == 1
            radar_systems(i).azimuth = main_radar_inward_angle;
        elseif i == 3
            radar_systems(i).azimuth = -main_radar_inward_angle;
        else
            radar_systems(i).azimuth = 0;
        end
        radar_systems(i).color = main_radar_colors(i, :);
        radar_systems(i).echo_power = [];
        radar_systems(i).detected_targets = [];
    end

    % 初始化子雷达系统
    for i = 1:sub_radar_count
        idx = main_radar_count + i;
        radar_systems(idx).id = idx;
        radar_systems(idx).type = 'sub';
        radar_systems(idx).name = sprintf('子雷达 %d', i);
        radar_systems(idx).position = sub_radar_positions(i, :);
        radar_systems(idx).range = sub_radar_range;
        radar_systems(idx).fov_h = sub_radar_fov_h;
        radar_systems(idx).fov_v = sub_radar_fov_v;
        radar_systems(idx).tilt = -10;
        radar_systems(idx).azimuth = 0;
        radar_systems(idx).color = sub_radar_color;
        radar_systems(idx).echo_power = [];
        radar_systems(idx).detected_targets = [];
    end

    % 初始化雷达回波显示对象
    radar_echo_texts = gobjects(1, total_radars);
    for i = 1:total_radars
        pos = radar_systems(i).position;
        radar_echo_texts(i) = text(pos(1), pos(2), pos(3) + radar_size(3) + 3, ...
                                  '无目标', 'HorizontalAlignment', 'center', ...
                                  'FontSize', 8, 'Color', radar_systems(i).color, ...
                                  'BackgroundColor', 'w', 'EdgeColor', 'k');
    end

    fprintf('雷达回波数据结构初始化完成。\n');
end

%% 8. 绘制主雷达覆盖区域
fprintf('正在绘制主雷达覆盖区域...\n');

for i = 1:main_radar_count
    pos = main_radar_positions(i, :);
    color = main_radar_colors(i, :);

    % 计算雷达朝向角度
    if i == 1  % 左侧雷达，向右倾斜
        azimuth_offset = main_radar_inward_angle;
    elseif i == 3  % 右侧雷达，向左倾斜
        azimuth_offset = -main_radar_inward_angle;
    else  % 中央雷达，直视前方
        azimuth_offset = 0;
    end

    % 绘制雷达覆盖锥体
    draw_radar_coverage(pos, main_radar_range, main_radar_fov_h, main_radar_fov_v, ...
                       main_radar_tilt, azimuth_offset, color, coverage_alpha);
end

%% 9. 绘制子雷达覆盖区域
fprintf('正在绘制子雷达覆盖区域...\n');

for i = 1:sub_radar_count
    pos = sub_radar_positions(i, :);

    % 子雷达向前方覆盖
    draw_radar_coverage(pos, sub_radar_range, sub_radar_fov_h, sub_radar_fov_v, ...
                       -10, 0, sub_radar_color, coverage_alpha);
end

%% 10. 添加图例和标题
title('高尔夫球场雷达监控系统 + 动态轨迹仿真', 'FontSize', 16, 'FontWeight', 'bold');

% 创建图例
legend_handles = [];
legend_labels = {};

% 添加主雷达图例
for i = 1:main_radar_count
    h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', main_radar_colors(i, :), ...
              'MarkerEdgeColor', 'k', 'MarkerSize', 10);
    legend_handles(end+1) = h;
    legend_labels{end+1} = main_radar_names{i};
end

% 添加子雷达图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', sub_radar_color, ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '子雷达系统';

% 添加击球区域图例
h = plot3(NaN, NaN, NaN, 's', 'MarkerFaceColor', [0.8, 0.6, 0.4], ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 8);
legend_handles(end+1) = h;
legend_labels{end+1} = '击球区域';

% 添加球轨迹图例
h = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', [0.5, 0.5, 0.5], ...
          'MarkerEdgeColor', 'k', 'MarkerSize', 6);
legend_handles(end+1) = h;
legend_labels{end+1} = '高尔夫球轨迹';

legend(legend_handles, legend_labels, 'Location', 'northeast', 'FontSize', 10);

%% 11. 动态仿真主循环
fprintf('\n正在开始动态轨迹仿真...\n');
fprintf('仿真开始... 按 Ctrl+C 停止。\n');

sim_time = 0;
num_finished_balls = 0; % 完成轨迹的球的数量

while num_finished_balls < n

    % --- 更新每个球的状态 ---
    for i = 1:n
        % 检查是否到了发射时间且尚未发射
        if sim_time >= balls(i).launch_time && ~balls(i).has_launched
            balls(i).has_launched = true;
            balls(i).is_active = true;
            % 将初始位置存入路径历史
            balls(i).path = balls(i).pos;
            fprintf('球 %d 发射！时间: %.2fs\n', i, sim_time);
        end

        % 如果球在空中，则更新其物理状态
        if balls(i).is_active
            % --- 计算物理量 ---
            v_mag = norm(balls(i).vel);
            F_drag = -0.5 * rho * A * Cd * v_mag * balls(i).vel;
            F_net = [0; 0; -m*g] + F_drag;
            a = F_net / m;

            % --- 更新速度和位置 (欧拉法) ---
            balls(i).vel = balls(i).vel + a * dt;
            balls(i).pos = balls(i).pos + balls(i).vel * dt;

            % 记录轨迹点
            balls(i).path = [balls(i).path, balls(i).pos];

            % 检查是否落地
            if balls(i).pos(3) < 0
                balls(i).is_active = false; % 球落地，停止更新
                num_finished_balls = num_finished_balls + 1; % 完成的球数加一
                fprintf('球 %d 落地！飞行时间: %.2fs, 距离: %.1fm\n', ...
                        i, sim_time - balls(i).launch_time, balls(i).pos(2));
            end
        end
    end

    % --- 雷达回波计算 ---
    if enable_radar_echo
        % 收集所有活跃球的位置
        active_balls = [];
        active_ball_ids = [];
        for i = 1:n
            if balls(i).is_active
                active_balls = [active_balls; balls(i).pos'];
                active_ball_ids = [active_ball_ids, i];
            end
        end

        % 为每个雷达系统计算回波
        for radar_idx = 1:total_radars
            radar = radar_systems(radar_idx);
            radar_systems(radar_idx).detected_targets = [];
            radar_systems(radar_idx).echo_power = [];

            if ~isempty(active_balls)
                % 计算雷达回波
                [detected_targets, echo_powers] = calculate_radar_echo(radar, active_balls, active_ball_ids, ...
                    radar_frequency, propagation_speed, sweep_bandwidth, detection_threshold);

                radar_systems(radar_idx).detected_targets = detected_targets;
                radar_systems(radar_idx).echo_power = echo_powers;

                % 更新雷达回波显示
                if ~isempty(detected_targets)
                    target_info = sprintf('目标: %d个\n功率: %.1fdBm', ...
                        length(detected_targets), max(echo_powers));
                else
                    target_info = '无目标';
                end
                set(radar_echo_texts(radar_idx), 'String', target_info);

                % 控制台输出检测信息（每秒输出一次）
                if ~isempty(detected_targets)
                    fprintf('[%.2fs] %s 检测到 %d 个目标，最强回波: %.1f dBm\n', ...
                        sim_time, radar.name, length(detected_targets), max(echo_powers));
                end
            else
                set(radar_echo_texts(radar_idx), 'String', '无目标');
            end
        end
    end

    % --- 更新绘图 ---
    for i = 1:n
        if balls(i).has_launched
            % 更新路径线条
            set(path_plots(i), 'XData', balls(i).path(1,:), ...
                               'YData', balls(i).path(2,:), ...
                               'ZData', balls(i).path(3,:));
            % 更新球的当前位置
            if balls(i).is_active
                set(ball_markers(i), 'XData', balls(i).pos(1), ...
                                     'YData', balls(i).pos(2), ...
                                     'ZData', balls(i).pos(3));
            else
                % 球落地后显示最终位置
                final_pos = balls(i).path(:, end);
                final_pos(3) = 0; % 确保在地面上
                set(ball_markers(i), 'XData', final_pos(1), ...
                                     'YData', final_pos(2), ...
                                     'ZData', final_pos(3));
            end
        end
    end

    % 刷新画布以显示更新
    drawnow limitrate;

    % 更新仿真时间
    sim_time = sim_time + dt;

    % 防止无限循环
    if sim_time > 30 % 30秒超时
        fprintf('仿真超时，强制结束。\n');
        break;
    end
end

% 调整最终视图以显示所有轨迹
axis tight; % 自动调整坐标轴范围以适应所有数据

fprintf('\n=== 仿真完成 ===\n');
fprintf('- 击球区域: %d 个\n', n);
fprintf('- 主雷达系统: %d 个\n', main_radar_count);
fprintf('- 子雷达系统: %d 个\n', sub_radar_count);
fprintf('- 完成轨迹的球: %d 个\n', num_finished_balls);
fprintf('- 总仿真时间: %.2f 秒\n', sim_time);
fprintf('- 雷达覆盖区域已显示（透明度: %.2f）\n', coverage_alpha);
fprintf('请旋转视角查看完整的3D布局和轨迹。\n');

%% 辅助函数定义区域

% 辅助函数：绘制雷达设备长方体
function draw_radar_box(center, size, color, alpha)
    % center: [x, y, z] 中心位置
    % size: [length, width, height] 尺寸
    % color: [r, g, b] 颜色
    % alpha: 透明度

    x = center(1) + [-size(1)/2, size(1)/2];
    y = center(2) + [-size(2)/2, size(2)/2];
    z = center(3) + [-size(3)/2, size(3)/2];

    % 定义长方体的8个顶点
    vertices = [
        x(1), y(1), z(1);  % 1
        x(2), y(1), z(1);  % 2
        x(2), y(2), z(1);  % 3
        x(1), y(2), z(1);  % 4
        x(1), y(1), z(2);  % 5
        x(2), y(1), z(2);  % 6
        x(2), y(2), z(2);  % 7
        x(1), y(2), z(2);  % 8
    ];

    % 定义6个面
    faces = [
        1, 2, 3, 4;  % 底面
        5, 6, 7, 8;  % 顶面
        1, 2, 6, 5;  % 前面
        3, 4, 8, 7;  % 后面
        1, 4, 8, 5;  % 左面
        2, 3, 7, 6;  % 右面
    ];

    % 绘制长方体
    patch('Vertices', vertices, 'Faces', faces, ...
          'FaceColor', color, 'FaceAlpha', alpha, ...
          'EdgeColor', 'k', 'LineWidth', 1);
end

% 辅助函数：绘制雷达覆盖区域
function draw_radar_coverage(radar_pos, range, fov_h, fov_v, tilt, azimuth, color, alpha)
    % radar_pos: [x, y, z] 雷达位置
    % range: 最大探测距离
    % fov_h: 水平视场角 (度)
    % fov_v: 垂直视场角 (度)
    % tilt: 俯仰角 (度，负数表示向下)
    % azimuth: 方位角偏移 (度)
    % color: 覆盖区域颜色
    % alpha: 透明度

    % 转换角度为弧度
    fov_h_rad = deg2rad(fov_h);
    fov_v_rad = deg2rad(fov_v);
    tilt_rad = deg2rad(tilt);
    azimuth_rad = deg2rad(azimuth);

    % 创建覆盖锥体的网格
    n_points = 20;
    theta = linspace(-fov_h_rad/2, fov_h_rad/2, n_points); % 水平角度
    phi = linspace(-fov_v_rad/2, fov_v_rad/2, n_points);   % 垂直角度
    r = linspace(0, range, 15); % 距离

    % 绘制覆盖锥体的边界面
    for i = 1:length(r)
        if i == 1
            continue; % 跳过原点
        end

        x_cone = [];
        y_cone = [];
        z_cone = [];

        for j = 1:length(theta)
            for k = 1:length(phi)
                % 球坐标到直角坐标的转换
                x_local = r(i) * cos(phi(k)) * sin(theta(j));
                y_local = r(i) * cos(phi(k)) * cos(theta(j));
                z_local = r(i) * sin(phi(k));

                % 应用俯仰角旋转
                x_rot = x_local;
                y_rot = y_local * cos(tilt_rad) - z_local * sin(tilt_rad);
                z_rot = y_local * sin(tilt_rad) + z_local * cos(tilt_rad);

                % 应用方位角旋转
                x_final = x_rot * cos(azimuth_rad) - y_rot * sin(azimuth_rad);
                y_final = x_rot * sin(azimuth_rad) + y_rot * cos(azimuth_rad);
                z_final = z_rot;

                % 转换到世界坐标系
                x_cone(end+1) = radar_pos(1) + x_final;
                y_cone(end+1) = radar_pos(2) + y_final;
                z_cone(end+1) = radar_pos(3) + z_final;
            end
        end

        % 绘制覆盖区域的点云（稀疏显示以提高性能）
        if mod(i, 3) == 0  % 每3个距离层显示一次
            scatter3(x_cone(1:5:end), y_cone(1:5:end), z_cone(1:5:end), ...
                    10, 'MarkerFaceColor', color, 'MarkerEdgeColor', 'none', ...
                    'MarkerFaceAlpha', alpha);
        end
    end

    % 绘制覆盖锥体的边界线
    % 水平边界线
    for phi_val = [-fov_v_rad/2, 0, fov_v_rad/2]
        x_line = [];
        y_line = [];
        z_line = [];

        for theta_val = linspace(-fov_h_rad/2, fov_h_rad/2, 20)
            x_local = range * cos(phi_val) * sin(theta_val);
            y_local = range * cos(phi_val) * cos(theta_val);
            z_local = range * sin(phi_val);

            % 应用旋转
            x_rot = x_local;
            y_rot = y_local * cos(tilt_rad) - z_local * sin(tilt_rad);
            z_rot = y_local * sin(tilt_rad) + z_local * cos(tilt_rad);

            x_final = x_rot * cos(azimuth_rad) - y_rot * sin(azimuth_rad);
            y_final = x_rot * sin(azimuth_rad) + y_rot * cos(azimuth_rad);
            z_final = z_rot;

            x_line(end+1) = radar_pos(1) + x_final;
            y_line(end+1) = radar_pos(2) + y_final;
            z_line(end+1) = radar_pos(3) + z_final;
        end

        plot3(x_line, y_line, z_line, 'Color', color, 'LineWidth', 1.5, ...
              'LineStyle', '--');
    end

    % 垂直边界线
    for theta_val = [-fov_h_rad/2, 0, fov_h_rad/2]
        x_line = [];
        y_line = [];
        z_line = [];

        for phi_val = linspace(-fov_v_rad/2, fov_v_rad/2, 20)
            x_local = range * cos(phi_val) * sin(theta_val);
            y_local = range * cos(phi_val) * cos(theta_val);
            z_local = range * sin(phi_val);

            % 应用旋转
            x_rot = x_local;
            y_rot = y_local * cos(tilt_rad) - z_local * sin(tilt_rad);
            z_rot = y_local * sin(tilt_rad) + z_local * cos(tilt_rad);

            x_final = x_rot * cos(azimuth_rad) - y_rot * sin(azimuth_rad);
            y_final = x_rot * sin(azimuth_rad) + y_rot * cos(azimuth_rad);
            z_final = z_rot;

            x_line(end+1) = radar_pos(1) + x_final;
            y_line(end+1) = radar_pos(2) + y_final;
            z_line(end+1) = radar_pos(3) + z_final;
        end

        plot3(x_line, y_line, z_line, 'Color', color, 'LineWidth', 1.5, ...
              'LineStyle', '--');
    end
end

% 辅助函数：计算雷达回波
function [detected_targets, echo_powers] = calculate_radar_echo(radar, target_positions, target_ids, ...
    radar_freq, c, bandwidth, threshold)
    % radar: 雷达系统结构
    % target_positions: 目标位置矩阵 [N x 3]
    % target_ids: 目标ID数组
    % radar_freq: 雷达频率 (Hz)
    % c: 光速 (m/s)
    % bandwidth: 扫频带宽 (Hz)
    % threshold: 检测门限 (dBm)

    detected_targets = [];
    echo_powers = [];

    if isempty(target_positions)
        return;
    end

    radar_pos = radar.position;

    for i = 1:size(target_positions, 1)
        target_pos = target_positions(i, :);

        % 计算距离
        distance = norm(target_pos - radar_pos);

        % 检查是否在雷达探测范围内
        if distance > radar.range
            continue;
        end

        % 检查是否在雷达视场角内
        if ~is_target_in_fov(radar_pos, target_pos, radar.tilt, radar.azimuth, ...
                             radar.fov_h, radar.fov_v)
            continue;
        end

        % 计算雷达截面积 (RCS) - 高尔夫球的典型RCS
        golf_ball_radius = 0.02135; % 高尔夫球半径 (m)
        rcs = pi * golf_ball_radius^2; % 简化的RCS计算

        % 计算路径损耗 (自由空间路径损耗)
        lambda = c / radar_freq;
        path_loss_db = 20 * log10(4 * pi * distance / lambda);

        % 计算雷达方程中的回波功率
        % P_r = P_t * G_t * G_r * lambda^2 * RCS / ((4*pi)^3 * R^4)
        % 简化计算，假设发射功率为20dBm，天线增益为20dB
        tx_power_dbm = 20;          % 发射功率 (dBm)
        antenna_gain_db = 20;       % 天线增益 (dB)

        % 计算接收功率
        rx_power_dbm = tx_power_dbm + 2 * antenna_gain_db + 10*log10(rcs) - 2*path_loss_db;

        % 添加一些随机噪声以模拟真实环境
        noise_std = 2; % 噪声标准差 (dB)
        rx_power_dbm = rx_power_dbm + noise_std * randn();

        % 检查是否超过检测门限
        if rx_power_dbm > threshold
            detected_targets = [detected_targets, target_ids(i)];
            echo_powers = [echo_powers, rx_power_dbm];
        end
    end
end

% 辅助函数：检查目标是否在雷达视场角内
function in_fov = is_target_in_fov(radar_pos, target_pos, tilt_deg, azimuth_deg, fov_h_deg, fov_v_deg)
    % 计算目标相对于雷达的方向向量
    direction = target_pos - radar_pos;
    direction = direction / norm(direction); % 归一化

    % 转换角度为弧度
    tilt_rad = deg2rad(tilt_deg);
    azimuth_rad = deg2rad(azimuth_deg);
    fov_h_rad = deg2rad(fov_h_deg);
    fov_v_rad = deg2rad(fov_v_deg);

    % 计算雷达指向的单位向量（考虑俯仰角和方位角）
    radar_direction = [sin(azimuth_rad) * cos(tilt_rad);
                      cos(azimuth_rad) * cos(tilt_rad);
                      sin(tilt_rad)];

    % 计算目标方向与雷达指向的夹角
    cos_angle = dot(direction, radar_direction);
    angle_rad = acos(max(-1, min(1, cos_angle))); % 限制在有效范围内

    % 简化的视场角检查（使用圆锥形近似）
    max_fov_rad = max(fov_h_rad, fov_v_rad) / 2;
    in_fov = angle_rad <= max_fov_rad;
end
