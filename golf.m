% =========================================================================
%                   高尔夫球轨迹动态仿真 (修正版)
% =========================================================================
%
% 功能:
% 1. 在一条直线上等间距设置n个高尔夫球。
% 2. 随机生成每个球的发射时间和发射仰角。
% 3. 打印每个球的初始参数。
% 4. 使用考虑空气阻力的物理模型计算轨迹。
% 5. 动态实时绘制每个球的飞行轨迹。
%
% =========================================================================

%% 1. 初始化和参数设置
clear; clc; close all;

% --- 用户可调参数 ---
n = 5;                  % 高尔夫球的数量
% v0 = 70;              % 【修改】这个固定的速度不再需要，可以注释或删除
min_v0 = 36;            % 【新增】最小初始速度 (m/s)
max_v0 = 100;           % 【新增】最大初始速度 (m/s)
tee_spacing = 5;        % 发球台之间的间距 (m)
min_angle = 9;          % 最小发射仰角 (度)
max_angle = 14;         % 最大发射仰角 (度)
min_azimuth = -3.0;     % 【新增】最小水平偏角 (度)，负数代表偏左
max_azimuth = 3.0;      % 【新增】最大水平偏角 (度)，正数代表偏右
max_launch_delay = 2.0; % 最大发射延迟时间 (s)
dt = 0.0005;            % 时间步长 (s)，即 0.5 ms

% --- 物理常量 ---
g = 9.81;               % 重力加速度 (m/s^2)
m = 0.0459;             % 高尔夫球质量 (kg)
r = 0.02135;            % 高尔夫球半径 (m)
A = pi * r^2;           % 高尔夫球横截面积 (m^2)
rho = 1.225;            % 空气密度 (kg/m^3)
Cd = 0.4;               % 阻力系数 (对于球体是典型值)

%% 2. 生成随机初始条件 (最终修正版 - 包含随机速度和偏角)
% 预分配结构体数组以提高效率
balls(n) = struct('id', [], 'pos', [], 'vel', [], 'path', [], ...
                  'launch_time', [], 'launch_angle', [], 'launch_speed', [], 'azimuth', [], ...
                  'is_active', false, 'has_launched', false);

% 生成发球台位置 (沿x轴分布)
x_start_positions = (0:n-1) * tee_spacing;

% 为每个球随机生成参数，并【完整地】初始化每个结构体元素
fprintf('----------- 高尔夫球初始参数 -----------\n');
for i = 1:n
    % --- 基本参数 ---
    balls(i).id = i;
    
    % --- 随机参数 ---
    % 垂直发射角 (仰角)
    elevation_deg = min_angle + (max_angle - min_angle) * rand();
    elevation_rad = deg2rad(elevation_deg);
    balls(i).launch_angle = elevation_deg;
    
    % 【新增】水平发射角 (偏角/方位角)
    azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();
    azimuth_rad = deg2rad(azimuth_deg);
    balls(i).azimuth = azimuth_deg;
    
    balls(i).launch_time = max_launch_delay * rand();
    
    % 为每个球生成一个在指定范围内的随机速度
    v0_rand = min_v0 + (max_v0 - min_v0) * rand();
    balls(i).launch_speed = v0_rand; 
    
    % --- 物理状态 ---
    balls(i).pos = [x_start_positions(i); 1.0; 0];
    
    % 【修改】使用三角函数将速度分解到X, Y, Z三个轴
    % 首先，将速度投影到水平面(v_xy)和垂直面(v_z)
    v_xy = v0_rand * cos(elevation_rad);
    v_z = v0_rand * sin(elevation_rad);
    
    % 然后，根据水平偏角(azimuth)将v_xy分解到vx和vy
    v_x = v_xy * sin(azimuth_rad);
    v_y = v_xy * cos(azimuth_rad);
    
    balls(i).vel = [v_x; v_y; v_z];
    
    % --- 初始化轨迹和状态标志 ---
    balls(i).path = []; % 轨迹历史初始化为空
    balls(i).is_active = false; % 初始状态为“未在空中”
    balls(i).has_launched = false; % 初始状态为“尚未发射”
    
    % --- 【修改】打印信息，加入水平偏角 ---
    fprintf('球 %d: 时间=%.2fs, 速度=%.2fm/s, 仰角=%.2f°, 偏角=%.2f°\n', ...
            i, balls(i).launch_time, balls(i).launch_speed, balls(i).launch_angle, balls(i).azimuth);
end
fprintf('------------------------------------------\n\n');

%% 3. 设置图形化界面
figure('Name', '高尔夫球轨迹仿真', 'NumberTitle', 'off', 'Color', 'w');
hold on;
grid on;
box on;

title('高尔夫球轨迹动态仿真');
xlabel('X 轴 - 发球台位置 (m)');
ylabel('Y 轴 - 飞行距离 (m)');
zlabel('Z 轴 - 高度 (m)');
view(30, 25); % 设置一个较好的3D视角
axis equal; % 使坐标轴比例相等，轨迹看起来更真实

% 【新增处】设置图形字体以支持中文显示
% 如果您是Windows系统，'Microsoft YaHei' 通常可用。
% 如果您是macOS系统，请尝试 'PingFang SC'。
% 如果均无效，请查找您系统中存在的其他中文字体。
set(gca, 'FontName', 'Microsoft YaHei', 'FontSize', 8);

% 为每个球的路径和当前位置创建绘图句柄
path_plots = gobjects(1, n);
ball_markers = gobjects(1, n);
colors = lines(n); % 为每个球生成不同的颜色

for i = 1:n
    % 路径线条 (初始为空)
    path_plots(i) = plot3(NaN, NaN, NaN, '-', 'Color', colors(i,:), 'LineWidth', 1.5);
    % 球的当前位置标记
    ball_markers(i) = plot3(NaN, NaN, NaN, 'o', 'MarkerFaceColor', colors(i,:), 'MarkerEdgeColor', 'k');
end

% 添加图例
legend_str = cell(1, n);
for i = 1:n
    legend_str{i} = sprintf('球 %d', i);
end
legend(path_plots, legend_str, 'Location', 'northeast');


%% 4. 仿真主循环
sim_time = 0;
num_finished_balls = 0; % 【新增处】完成轨迹的球的数量

fprintf('仿真开始... 按 Ctrl+C 停止。\n');

% 【修改处】修改了主循环的判断条件
while num_finished_balls < n
    
    % --- 更新每个球的状态 ---
    for i = 1:n
        % 检查是否到了发射时间且尚未发射
        if sim_time >= balls(i).launch_time & ~balls(i).has_launched
            balls(i).has_launched = true;
            balls(i).is_active = true;
            % 将初始位置存入路径历史
            balls(i).path = balls(i).pos;
        end
        
        % 如果球在空中，则更新其物理状态
        if balls(i).is_active
            % --- 计算物理量 ---
            v_mag = norm(balls(i).vel);
            F_drag = -0.5 * rho * A * Cd * v_mag * balls(i).vel;
            F_net = [0; 0; -m*g] + F_drag;
            a = F_net / m;
            
            % --- 更新速度和位置 (欧拉法) ---
            balls(i).vel = balls(i).vel + a * dt;
            balls(i).pos = balls(i).pos + balls(i).vel * dt;
            
            % 记录轨迹点
            balls(i).path = [balls(i).path, balls(i).pos];
            
            % 【修改处】检查是否落地，如果落地则更新计数器
            if balls(i).pos(3) < 0
                balls(i).is_active = false; % 球落地，停止更新
                num_finished_balls = num_finished_balls + 1; % 完成的球数加一
            end
        end
    end
    
    % --- 更新绘图 ---
    for i = 1:n
        if balls(i).has_launched
            % 更新路径线条
            set(path_plots(i), 'XData', balls(i).path(1,:), ...
                               'YData', balls(i).path(2,:), ...
                               'ZData', balls(i).path(3,:));
            % 更新球的当前位置
            set(ball_markers(i), 'XData', balls(i).pos(1), ...
                                 'YData', balls(i).pos(2), ...
                                 'ZData', balls(i).pos(3));
        end
    end
    
    % 刷新画布以显示更新
    drawnow limitrate;
    
    % 更新仿真时间
    sim_time = sim_time + dt;
end

% 调整最终视图以显示所有轨迹
axis tight; % 自动调整坐标轴范围以适应所有数据
fprintf('仿真结束。\n');