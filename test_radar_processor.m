%% 高级帧模式雷达数据处理器测试脚本
% 用于测试advanced_frame_radar_processor.m的基本功能

clear; clc; close all;

fprintf('=== 高级帧模式雷达数据处理器测试 ===\n\n');

%% 1. 检查必需文件
fprintf('1. 检查必需文件...\n');

required_files = {
    'data_reduced_precision_advanced_frame.mat',
    'advanced_frame_radar_processor.m'
};

all_files_exist = true;
for i = 1:length(required_files)
    if exist(required_files{i}, 'file')
        fprintf('   ✓ %s 存在\n', required_files{i});
    else
        fprintf('   ❌ %s 不存在\n', required_files{i});
        all_files_exist = false;
    end
end

if ~all_files_exist
    fprintf('\n请确保以下文件存在：\n');
    fprintf('1. 先运行 Copy_of_golf_of_MIMO_of_main.m 生成数据文件\n');
    fprintf('2. 确保 advanced_frame_radar_processor.m 在当前目录\n');
    return;
end

%% 2. 测试数据加载
fprintf('\n2. 测试数据加载...\n');
try
    data = load('data_reduced_precision_advanced_frame.mat');

    if ~isfield(data, 'RawDataMatrix') || ~isfield(data, 'fmcw')
        error('数据文件格式不正确');
    end

    if ~data.fmcw.advancedFrameMode
        error('数据不是高级帧模式生成的');
    end

    fprintf('   ✓ 数据加载成功\n');
    fprintf('   数据维度: [%d, %d, %d, %d]\n', size(data.RawDataMatrix));
    fprintf('   高级帧模式: %s\n', mat2str(data.fmcw.advancedFrameMode));

catch ME
    fprintf('   ❌ 数据加载失败: %s\n', ME.message);
    return;
end

%% 3. 测试参数提取
fprintf('\n3. 测试参数提取...\n');
try
    fmcw = data.fmcw;

    % 检查必需的参数
    required_params = {
        'NumOfChirpInOneFrame', 'NumOfChirpInOneSubframe2',
        'radarFrequency', 'radarFrequency2',
        'Bw', 'Bw2',
        'chirpTRandEndime', 'chirpTRandEndime2',
        'Tidle', 'Tidle2',
        'adcSamplingFrequency', 'propagationSpeed'
    };

    for i = 1:length(required_params)
        if isfield(fmcw, required_params{i})
            fprintf('   ✓ %s: %g\n', required_params{i}, fmcw.(required_params{i}));
        else
            fprintf('   ❌ 缺少参数: %s\n', required_params{i});
        end
    end

catch ME
    fprintf('   ❌ 参数提取失败: %s\n', ME.message);
    return;
end

%% 4. 测试数据分离
fprintf('\n4. 测试数据分离...\n');
try
    RawDataMatrix = data.RawDataMatrix;

    subframe1_chirps = fmcw.NumOfChirpInOneFrame;
    subframe2_chirps = fmcw.NumOfChirpInOneSubframe2;
    total_chirps = subframe1_chirps + subframe2_chirps;

    if size(RawDataMatrix, 2) ~= total_chirps
        error('数据维度不匹配');
    end

    % 分离子帧数据
    subframe1_data = RawDataMatrix(:, 1:subframe1_chirps, :, :);
    subframe2_data = RawDataMatrix(:, (subframe1_chirps+1):end, :, :);

    fprintf('   ✓ 数据分离成功\n');
    fprintf('   第一子帧数据: [%d, %d, %d, %d]\n', size(subframe1_data));
    fprintf('   第二子帧数据: [%d, %d, %d, %d]\n', size(subframe2_data));

catch ME
    fprintf('   ❌ 数据分离失败: %s\n', ME.message);
    return;
end

%% 5. 测试基本FFT处理
fprintf('\n5. 测试基本FFT处理...\n');
try
    % 取第一帧第一子帧数据进行测试
    test_frame = squeeze(subframe1_data(1, :, :, :));
    [num_chirps, num_samples, num_rx] = size(test_frame);

    fprintf('   测试帧数据维度: [%d, %d, %d]\n', num_chirps, num_samples, num_rx);

    % 简单的距离FFT测试
    range_fft = fft(test_frame, 512, 2);
    range_fft = fftshift(range_fft, 2);
    range_fft = range_fft(:, 1:512, :);

    % 简单的速度FFT测试
    vel_fft = fft(range_fft, 128, 1);
%     vel_fft = fftshift(vel_fft, 1);

    % 生成简单的RD图
    rd_map = squeeze(sum(abs(vel_fft).^2, 3));
    rd_map_db = 10 * log10(rd_map + eps);

    fprintf('   ✓ FFT处理成功\n');
    fprintf('   RD图维度: [%d, %d]\n', size(rd_map_db));
    fprintf('   RD图动态范围: %.1f dB 到 %.1f dB\n', min(rd_map_db(:)), max(rd_map_db(:)));

catch ME
    fprintf('   ❌ FFT处理失败: %s\n', ME.message);
    return;
end

%% 6. 测试可视化
fprintf('\n6. 测试可视化...\n');
try
    % 设置中文字体支持
    try
        if ispc
            set(0, 'DefaultAxesFontName', 'SimHei');
            set(0, 'DefaultTextFontName', 'SimHei');
        end
    catch
        % 忽略字体设置错误
    end

    figure('Name', 'Test RD Map', 'Position', [100, 100, 800, 600]);

    % 显示测试RD图 - 修正维度显示
    imagesc(rd_map_db);
    colorbar;
    title('Test RD Map (Frame 1, Subframe 1)', 'FontSize', 12);
    xlabel('Range bins', 'FontSize', 10);
    ylabel('Velocity bins', 'FontSize', 10);
    colormap jet;
    grid on;

    fprintf('   ✓ 可视化测试成功\n');
    fprintf('   RD图动态范围: %.1f dB\n', max(rd_map_db(:)) - min(rd_map_db(:)));

catch ME
    fprintf('   ❌ 可视化测试失败: %s\n', ME.message);
    return;
end

%% 7. 运行完整处理器
fprintf('\n7. 准备运行完整处理器...\n');
fprintf('   所有测试通过！\n');
fprintf('   现在可以运行: advanced_frame_radar_processor()\n\n');

% 询问是否立即运行
choice = questdlg('是否立即运行完整的雷达数据处理器？', ...
    '运行选择', '是', '否', '否');

if strcmp(choice, '是')
    fprintf('启动高级帧模式雷达数据处理器...\n\n');
    try
        advanced_frame_radar_processor();
    catch ME
        fprintf('❌ 处理器运行失败: %s\n', ME.message);
    end
else
    fprintf('测试完成。要运行完整处理器，请执行: advanced_frame_radar_processor()\n');
end

fprintf('\n=== 测试完成 ===\n');
