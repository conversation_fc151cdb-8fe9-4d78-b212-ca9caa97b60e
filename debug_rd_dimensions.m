% =========================================================================
%                   RD图维度调试脚本
% =========================================================================
%
% 用于调试雷达数据立方体和RD图处理中的维度问题
%
% =========================================================================

%% 1. 参数设置
clear; clc; close all;

fprintf('开始调试RD图维度问题...\n');

% FMCW雷达参数
num_chirps_per_frame = 64;
num_virtual_antennas = 8;
num_adc_samples = 268;
range_fft_size = 512;
vel_fft_size = 128;

fprintf('参数设置:\n');
fprintf('  num_chirps_per_frame = %d\n', num_chirps_per_frame);
fprintf('  num_virtual_antennas = %d\n', num_virtual_antennas);
fprintf('  num_adc_samples = %d\n', num_adc_samples);
fprintf('  range_fft_size = %d\n', range_fft_size);
fprintf('  vel_fft_size = %d\n', vel_fft_size);

%% 2. 创建测试数据
fprintf('\n创建测试雷达数据立方体...\n');

% 创建雷达数据立方体
radar_cube = zeros(num_chirps_per_frame, num_virtual_antennas, num_adc_samples);

% 添加一些测试信号
for chirp_idx = 1:num_chirps_per_frame
    for rx_idx = 1:num_virtual_antennas
        % 添加距离为50m的目标信号
        target_range_bin = 20;
        radar_cube(chirp_idx, rx_idx, target_range_bin) = 1.0 * exp(1j * 2 * pi * rand());
        
        % 添加噪声
        noise_real = 0.1 * randn(1, num_adc_samples);
        noise_imag = 0.1 * randn(1, num_adc_samples);
        noise = complex(noise_real, noise_imag);
        radar_cube(chirp_idx, rx_idx, :) = squeeze(radar_cube(chirp_idx, rx_idx, :))' + noise;
    end
end

fprintf('雷达数据立方体维度: [%d, %d, %d]\n', size(radar_cube));

%% 3. 测试RD图处理
fprintf('\n测试RD图处理...\n');

try
    % 调用RD图生成函数
    [rd_map, range_bins, vel_bins] = debug_generate_rd_map(radar_cube, range_fft_size, vel_fft_size);
    
    fprintf('RD图生成成功！\n');
    fprintf('RD图维度: [%d, %d]\n', size(rd_map));
    fprintf('距离bins数量: %d\n', length(range_bins));
    fprintf('速度bins数量: %d\n', length(vel_bins));
    
    % 可视化结果
    figure('Name', '调试RD图结果', 'Position', [100, 100, 800, 600]);
    
    subplot(2, 2, 1);
    imagesc(rd_map);
    colorbar;
    title('RD Map (原始)');
    xlabel('距离bins');
    ylabel('速度bins');
    
    subplot(2, 2, 2);
    plot(max(rd_map, [], 1));
    title('距离剖面');
    xlabel('距离bins');
    ylabel('功率 (dB)');
    grid on;
    
    subplot(2, 2, 3);
    plot(max(rd_map, [], 2));
    title('速度剖面');
    xlabel('速度bins');
    ylabel('功率 (dB)');
    grid on;
    
    subplot(2, 2, 4);
    surf(rd_map);
    title('RD Map 3D');
    xlabel('距离bins');
    ylabel('速度bins');
    zlabel('功率 (dB)');
    
    fprintf('可视化完成。\n');
    
catch ME
    fprintf('错误发生: %s\n', ME.message);
    fprintf('错误位置: %s, 行 %d\n', ME.stack(1).name, ME.stack(1).line);
    
    % 打印详细的维度信息
    fprintf('\n详细维度信息:\n');
    fprintf('radar_cube 维度: [%d, %d, %d]\n', size(radar_cube));
    
    % 尝试手动检查每一步
    fprintf('\n手动检查处理步骤:\n');
    
    % 步骤1: 检查窗函数
    range_window = hamming(num_adc_samples);
    fprintf('range_window 维度: [%d, %d]\n', size(range_window));
    
    range_window_3d = repmat(reshape(range_window, 1, 1, num_adc_samples), [num_chirps_per_frame, num_virtual_antennas, 1]);
    fprintf('range_window_3d 维度: [%d, %d, %d]\n', size(range_window_3d));
    
    % 步骤2: 检查窗函数应用
    try
        windowed_data = radar_cube .* range_window_3d;
        fprintf('windowed_data 维度: [%d, %d, %d]\n', size(windowed_data));
    catch ME2
        fprintf('窗函数应用失败: %s\n', ME2.message);
    end
end

%% 4. 辅助函数定义

function [rd_map, range_bins, vel_bins] = debug_generate_rd_map(frame_data, range_fft_size, vel_fft_size)
    fprintf('  进入RD图生成函数...\n');
    
    % 获取维度
    [num_chirps, num_rx, num_samples] = size(frame_data);
    fprintf('  输入数据维度: [%d, %d, %d]\n', num_chirps, num_rx, num_samples);
    
    % 检查输入数据维度
    if ndims(frame_data) ~= 3
        error('frame_data must be 3D: [chirps, rx, samples]');
    end
    
    % 距离FFT (对samples维度)
    fprintf('  创建距离窗函数...\n');
    range_window = hamming(num_samples);
    fprintf('  range_window 维度: [%d, %d]\n', size(range_window));
    
    range_window_3d = repmat(reshape(range_window, 1, 1, num_samples), [num_chirps, num_rx, 1]);
    fprintf('  range_window_3d 维度: [%d, %d, %d]\n', size(range_window_3d));
    
    % 应用窗函数
    fprintf('  应用距离窗函数...\n');
    windowed_data = frame_data .* range_window_3d;
    fprintf('  windowed_data 维度: [%d, %d, %d]\n', size(windowed_data));
    
    % 距离FFT
    fprintf('  执行距离FFT...\n');
    range_fft = fft(windowed_data, range_fft_size, 3);
    fprintf('  range_fft 维度: [%d, %d, %d]\n', size(range_fft));
    
    range_fft = range_fft(:, :, 1:range_fft_size/2); % 只取正频率部分
    fprintf('  range_fft (正频率) 维度: [%d, %d, %d]\n', size(range_fft));
    
    % 速度FFT (对chirps维度)
    fprintf('  创建速度窗函数...\n');
    vel_window = hamming(num_chirps);
    fprintf('  vel_window 维度: [%d, %d]\n', size(vel_window));
    
    vel_window_3d = repmat(reshape(vel_window, num_chirps, 1, 1), [1, num_rx, range_fft_size/2]);
    fprintf('  vel_window_3d 维度: [%d, %d, %d]\n', size(vel_window_3d));
    
    % 应用窗函数
    fprintf('  应用速度窗函数...\n');
    windowed_range_data = range_fft .* vel_window_3d;
    fprintf('  windowed_range_data 维度: [%d, %d, %d]\n', size(windowed_range_data));
    
    % 速度FFT
    fprintf('  执行速度FFT...\n');
    vel_fft = fft(windowed_range_data, vel_fft_size, 1);
    fprintf('  vel_fft 维度: [%d, %d, %d]\n', size(vel_fft));
    
    vel_fft = fftshift(vel_fft, 1);
    fprintf('  vel_fft (频移后) 维度: [%d, %d, %d]\n', size(vel_fft));
    
    % 多天线合并
    fprintf('  多天线合并...\n');
    rd_map = squeeze(sum(abs(vel_fft).^2, 2));
    fprintf('  rd_map (合并后) 维度: [%d, %d]\n', size(rd_map));
    
    % 确保rd_map是2D矩阵
    if size(rd_map, 1) == 1 || size(rd_map, 2) == 1
        fprintf('  重新整形rd_map...\n');
        rd_map = reshape(rd_map, vel_fft_size, range_fft_size/2);
        fprintf('  rd_map (重新整形后) 维度: [%d, %d]\n', size(rd_map));
    end
    
    % 转换为dB
    fprintf('  转换为dB...\n');
    rd_map = 10 * log10(rd_map + eps);
    
    % 生成坐标轴
    range_bins = 1:range_fft_size/2;
    vel_bins = 1:vel_fft_size;
    
    fprintf('  RD图生成完成！\n');
end

fprintf('\n调试脚本执行完成。\n');
