# 高级帧模式雷达数据处理器使用说明

## 概述
`advanced_frame_radar_processor.m` 是一个专门用于处理高级帧模式FMCW雷达数据的MATLAB程序。它能够解析双子帧数据结构，生成Range-Doppler图，并实现速度解模糊功能。

## 功能特性

### 1. 数据加载和解析
- 自动读取 `data_reduced_precision_advanced_frame.mat` 文件
- 验证数据格式和完整性
- 智能分离双子帧数据结构

### 2. 分子帧RD图生成
- 独立处理两个子帧的数据
- 应用汉明窗函数优化频谱特性
- 生成高质量的Range-Doppler图

### 3. 动态可视化界面
- 实时显示双子帧RD图
- 播放控制（播放/暂停/单步）
- 帧滑块快速导航
- 子帧差异分析

### 4. 速度解模糊算法
- 基于中国剩余定理的解模糊算法
- 利用双子帧的频率差异
- 实时显示解模糊结果

### 5. 结果保存和报告
- 自动生成处理报告
- 保存解模糊速度数据
- 可选保存RD图序列

## 文件结构

```
高级帧模式雷达处理系统/
├── advanced_frame_radar_processor.m    # 主处理程序
├── test_radar_processor.m              # 测试脚本
├── data_reduced_precision_advanced_frame.mat  # 输入数据文件
└── 雷达数据处理器使用说明.md          # 本说明文档
```

## 使用步骤

### 步骤1：准备数据
确保已运行高级帧模式数据生成程序：
```matlab
% 设置高级帧模式
fmcw.advancedFrameMode = true;
% 运行数据生成
run('Copy_of_golf_of_MIMO_of_main.m');
```

### 步骤2：测试环境
运行测试脚本验证环境：
```matlab
run('test_radar_processor.m');
```

### 步骤3：启动处理器
运行主处理程序：
```matlab
advanced_frame_radar_processor();
```

## 界面操作指南

### 控制面板
- **播放按钮**: 自动播放所有帧
- **暂停按钮**: 暂停播放
- **单步按钮**: 逐帧处理
- **帧滑块**: 快速跳转到指定帧
- **速度解模糊**: 开启/关闭解模糊功能
- **保存结果**: 保存处理结果

### 显示窗口
1. **左上**: 第一子帧RD图 (77 GHz)
2. **右上**: 第二子帧RD图 (77.5 GHz)
3. **左下**: 子帧差异图
4. **右下**: 解模糊速度时间序列

## 技术参数

### 处理参数
- **距离FFT大小**: 512点
- **速度FFT大小**: 128点
- **窗函数**: 汉明窗
- **动态范围**: >60dB

### 解模糊参数
- **搜索范围**: ±200 m/s
- **精度**: 0.1 m/s
- **算法**: 中国剩余定理

## 输出文件

### 1. 解模糊速度数据
文件名: `deambiguated_velocities_YYYYMMDD_HHMMSS.mat`
内容:
- `deambiguated_velocities`: 解模糊速度数组
- `params1`: 第一子帧参数
- `params2`: 第二子帧参数

### 2. RD图序列（可选）
文件名: `rd_maps_YYYYMMDD_HHMMSS.mat`
内容:
- `rd_maps1`: 第一子帧RD图序列
- `rd_maps2`: 第二子帧RD图序列

### 3. 处理报告
文件名: `processing_report_YYYYMMDD_HHMMSS.txt`
内容:
- 数据统计信息
- 处理参数
- 解模糊结果统计

## 故障排除

### 常见问题

#### 1. 数据文件不存在
**错误**: `数据文件 data_reduced_precision_advanced_frame.mat 不存在`
**解决**: 先运行 `Copy_of_golf_of_MIMO_of_main.m` 生成数据

#### 2. 不是高级帧模式数据
**错误**: `加载的数据不是高级帧模式数据`
**解决**: 确保在数据生成时设置 `fmcw.advancedFrameMode = true`

#### 3. 内存不足
**错误**: `Out of memory`
**解决**: 
- 减少处理的帧数
- 降低FFT大小
- 关闭RD图序列保存

#### 4. 显示问题
**错误**: 图像显示异常
**解决**:
- 检查MATLAB图形驱动
- 调整窗口大小
- 重启MATLAB

### 性能优化

#### 1. 内存优化
```matlab
% 在处理大数据时，可以分批处理
batch_size = 10; % 每批处理10帧
```

#### 2. 速度优化
```matlab
% 降低FFT大小以提高速度
range_fft_size = 256; % 默认512
vel_fft_size = 64;    % 默认128
```

## 技术原理

### 1. 双子帧处理
- 第一子帧: 77 GHz, 56 MHz带宽
- 第二子帧: 77.5 GHz, 54 MHz带宽
- 时序: 子帧1 → 间隔 → 子帧2

### 2. 速度解模糊原理
利用两个子帧的不同载频产生的不同速度模糊间隔：
```
V_unambiguous = CRT(V1_ambiguous, V2_ambiguous, T1, T2)
```
其中：
- `V1_ambiguous`, `V2_ambiguous`: 两子帧的模糊速度
- `T1`, `T2`: 两子帧的速度模糊间隔
- `CRT`: 中国剩余定理算法

### 3. RD图生成流程
1. 距离FFT: 时域 → 距离域
2. 速度FFT: 慢时间 → 多普勒域
3. 多天线合并: 相干积累
4. 对数变换: 线性 → dB

## 扩展功能

### 1. 自定义处理参数
可以修改以下参数来适应不同需求：
```matlab
range_fft_size = 1024;  % 提高距离分辨率
vel_fft_size = 256;     % 提高速度分辨率
```

### 2. 添加新的解模糊算法
可以在 `velocity_deambiguation` 函数中实现新算法

### 3. 增强可视化
可以添加更多的显示选项和分析工具

## 联系支持
如有问题或建议，请参考代码注释或联系开发团队。

---
**版本**: 1.0  
**更新日期**: 2025年  
**兼容性**: MATLAB R2018b及以上版本
