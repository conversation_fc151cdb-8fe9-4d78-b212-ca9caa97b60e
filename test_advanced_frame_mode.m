%% 测试高级帧模式的基本功能
% 这个脚本用于验证高级帧模式的配置是否正确

clear; clc; close all;

fprintf('=== 测试高级帧模式配置 ===\n\n');

%% 构造MIMO-FMCW雷达
fmcw = MIMORADAR;

%% 物理场景
fmcw.propagationSpeed = physconst('LightSpeed');

%% FMCW波形参数设计 - 传统模式（第一子帧）
fmcw.radarFrequency = 77e9;
fmcw.chirpTRandEndime = 56e-6;
fmcw.Tidle = 14e-6;
fmcw.sweepBw = 1e12;
fmcw.Bw = fmcw.chirpTRandEndime * fmcw.sweepBw;
fmcw.NumOfChirpInOneFrame = 64;
fmcw.TimeOfFrame = 40e-3;
fmcw.activateTimeInOneFrame = (fmcw.chirpTRandEndime + fmcw.Tidle)* fmcw.NumOfChirpInOneFrame;
fmcw.TimeOfFrame = fmcw.activateTimeInOneFrame;

%% 高级帧模式配置测试
fmcw.advancedFrameMode = true;

if fmcw.advancedFrameMode
    % 第二子帧的FMCW参数配置
    fmcw.radarFrequency2 = 77.5e9;
    fmcw.chirpTRandEndime2 = 60e-6;
    fmcw.Tidle2 = 12e-6;
    fmcw.sweepBw2 = 0.9e12;
    fmcw.Bw2 = fmcw.chirpTRandEndime2 * fmcw.sweepBw2;
    fmcw.NumOfChirpInOneSubframe2 = 64;
    
    % 计算双子帧的总激活时间
    fmcw.activateTimeSubframe1 = (fmcw.chirpTRandEndime + fmcw.Tidle) * fmcw.NumOfChirpInOneFrame;
    fmcw.activateTimeSubframe2 = (fmcw.chirpTRandEndime2 + fmcw.Tidle2) * fmcw.NumOfChirpInOneSubframe2;
    fmcw.activateTimeInOneFrame = fmcw.activateTimeSubframe1 + fmcw.activateTimeSubframe2;
    fmcw.TimeOfFrame = fmcw.activateTimeInOneFrame;
    
    fmcw.subframeGap = 1e-6;
    fmcw.TimeOfFrame = fmcw.TimeOfFrame + fmcw.subframeGap;
    
    fprintf('高级帧模式配置验证:\n');
    fprintf('  第一子帧: %.1f MHz带宽, %.1f us chirp时间, %d chirps\n', ...
        fmcw.Bw/1e6, fmcw.chirpTRandEndime*1e6, fmcw.NumOfChirpInOneFrame);
    fprintf('  第二子帧: %.1f MHz带宽, %.1f us chirp时间, %d chirps\n', ...
        fmcw.Bw2/1e6, fmcw.chirpTRandEndime2*1e6, fmcw.NumOfChirpInOneSubframe2);
    fprintf('  总帧时间: %.2f ms\n', fmcw.TimeOfFrame*1000);
    fprintf('  激活时间比例: %.1f%%\n', (fmcw.activateTimeInOneFrame/fmcw.TimeOfFrame)*100);
end

%% 天线参数
fmcw.numberOfTransmitAntennas = 2;
fmcw.numberOfReceiverAntennas = 4;
fmcw.numberOfvirtueRxannate = fmcw.numberOfTransmitAntennas * fmcw.numberOfReceiverAntennas;

%% 测试数据矩阵大小计算
fmcw.NumOfFrame = 10; % 测试用的小帧数

if fmcw.advancedFrameMode
    total_chirps_per_frame = fmcw.NumOfChirpInOneFrame + fmcw.NumOfChirpInOneSubframe2;
    fprintf('\n数据矩阵测试:\n');
    fprintf('  每帧总chirps: %d (子帧1: %d + 子帧2: %d)\n', ...
        total_chirps_per_frame, fmcw.NumOfChirpInOneFrame, fmcw.NumOfChirpInOneSubframe2);
    fprintf('  预期数据矩阵大小: [%d, %d, %d, %d]\n', ...
        fmcw.NumOfFrame, total_chirps_per_frame, fmcw.numberOfvirtueRxannate, 268);
end

%% 测试时间轴计算
fprintf('\n时间轴计算测试:\n');
tt = [];
for frame=1:3  % 只测试前3帧
    if fmcw.advancedFrameMode
        % 第一子帧
        for chirp=1:fmcw.NumOfChirpInOneFrame
            t = (frame-1)*fmcw.TimeOfFrame + (chirp-1)*(fmcw.chirpTRandEndime + fmcw.Tidle) * fmcw.numberOfTransmitAntennas;
            tt = [tt, t];
        end
        
        % 第二子帧
        subframe2_start_time = fmcw.activateTimeSubframe1 + fmcw.subframeGap;
        for chirp=1:fmcw.NumOfChirpInOneSubframe2
            t = (frame-1)*fmcw.TimeOfFrame + subframe2_start_time + (chirp-1)*(fmcw.chirpTRandEndime2 + fmcw.Tidle2) * fmcw.numberOfTransmitAntennas;
            tt = [tt, t];
        end
    end
end

fprintf('  前3帧时间点数量: %d\n', length(tt));
fprintf('  时间范围: %.6f s 到 %.6f s\n', min(tt), max(tt));

fprintf('\n=== 高级帧模式配置测试完成 ===\n');
fprintf('配置验证成功！可以运行完整的仿真程序。\n');
